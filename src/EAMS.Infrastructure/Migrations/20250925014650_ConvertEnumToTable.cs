﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace EAMS.Infrastructure.Migrations
{
    /// <inheritdoc />
    public partial class ConvertEnumToTable : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "Duration",
                table: "Accommodations");

            migrationBuilder.RenameColumn(
                name: "AmenityType",
                table: "Amenities",
                newName: "AmenityTypeId");

            migrationBuilder.RenameColumn(
                name: "Region",
                table: "Accommodations",
                newName: "RegionId");

            migrationBuilder.RenameColumn(
                name: "Density",
                table: "Accommodations",
                newName: "DensityId");

            migrationBuilder.RenameColumn(
                name: "AccommodationType",
                table: "Accommodations",
                newName: "AccommodationTypeId");

            migrationBuilder.CreateTable(
                name: "AccommodationType",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    CreatedAt = table.Column<DateTime>(type: "datetime2", nullable: false),
                    UpdatedAt = table.Column<DateTime>(type: "datetime2", nullable: false),
                    DiscardedAt = table.Column<DateTime>(type: "datetime2", nullable: true),
                    Name = table.Column<string>(type: "nvarchar(max)", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_AccommodationType", x => x.Id);
                });

            migrationBuilder.InsertData(
                table: "AccommodationType",
                columns: new[] { "Id", "Name", "CreatedAt", "UpdatedAt" },
                values: new object[,]
                {
                    {1, "Hotel", DateTime.UtcNow, DateTime.UtcNow },
                    {2, "Apartment Hotel", DateTime.UtcNow, DateTime.UtcNow },
                    {3, "Caravan Park", DateTime.UtcNow, DateTime.UtcNow },
                    {4, "Homestay", DateTime.UtcNow, DateTime.UtcNow },
                    {5, "Hostel", DateTime.UtcNow, DateTime.UtcNow },
                    {6, "Motel/Motor Inn", DateTime.UtcNow, DateTime.UtcNow },
                    {7, "Rooming House", DateTime.UtcNow, DateTime.UtcNow },
                    {8, "Rooming House Unregistered", DateTime.UtcNow, DateTime.UtcNow },
                    {9, "Serviced Apartment", DateTime.UtcNow, DateTime.UtcNow },
                    {10, "Single Unit", DateTime.UtcNow, DateTime.UtcNow },
                    {11, "SRS (Pension Level)", DateTime.UtcNow, DateTime.UtcNow },
                    {12, "SRS (Above Pension Level)", DateTime.UtcNow, DateTime.UtcNow }
                }
            );

            migrationBuilder.CreateTable(
                name: "AmenityType",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    CreatedAt = table.Column<DateTime>(type: "datetime2", nullable: false),
                    UpdatedAt = table.Column<DateTime>(type: "datetime2", nullable: false),
                    DiscardedAt = table.Column<DateTime>(type: "datetime2", nullable: true),
                    Name = table.Column<string>(type: "nvarchar(max)", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_AmenityType", x => x.Id);
                });

            migrationBuilder.InsertData(
                table: "AmenityType",
                columns: new[] { "Id", "Name", "CreatedAt", "UpdatedAt" },
                values: new object[,]
                {
                    {1, "Characteristic", DateTime.UtcNow, DateTime.UtcNow },
                    {2, "Safety", DateTime.UtcNow, DateTime.UtcNow },
                    {3, "Amenity", DateTime.UtcNow, DateTime.UtcNow },
                    {4, "Additional", DateTime.UtcNow, DateTime.UtcNow }
                });

            migrationBuilder.CreateTable(
                name: "Density",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    CreatedAt = table.Column<DateTime>(type: "datetime2", nullable: false),
                    UpdatedAt = table.Column<DateTime>(type: "datetime2", nullable: false),
                    DiscardedAt = table.Column<DateTime>(type: "datetime2", nullable: true),
                    Name = table.Column<string>(type: "nvarchar(max)", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Density", x => x.Id);
                });

            migrationBuilder.InsertData(
                table: "Density",
                columns: new[] { "Id", "Name", "CreatedAt", "UpdatedAt" },
                values: new object[,]
                {
                    {1, "Low", DateTime.UtcNow, DateTime.UtcNow },
                    {2, "Medium", DateTime.UtcNow, DateTime.UtcNow },
                    {3, "High", DateTime.UtcNow, DateTime.UtcNow }
                });

            migrationBuilder.CreateTable(
                name: "Duration",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    CreatedAt = table.Column<DateTime>(type: "datetime2", nullable: false),
                    UpdatedAt = table.Column<DateTime>(type: "datetime2", nullable: false),
                    DiscardedAt = table.Column<DateTime>(type: "datetime2", nullable: true),
                    Name = table.Column<string>(type: "nvarchar(max)", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Duration", x => x.Id);
                });

            migrationBuilder.InsertData(
                table: "Duration",
                columns: new[] { "Id", "Name", "CreatedAt", "UpdatedAt" },
                values: new object[,]
                {
                    {1, "Single Night Stay", DateTime.UtcNow, DateTime.UtcNow },
                    {2, "Short Term Stay", DateTime.UtcNow, DateTime.UtcNow },
                    {3, "Long Term Stay", DateTime.UtcNow, DateTime.UtcNow }
                });


            migrationBuilder.CreateTable(
                name: "Region",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    CreatedAt = table.Column<DateTime>(type: "datetime2", nullable: false),
                    UpdatedAt = table.Column<DateTime>(type: "datetime2", nullable: false),
                    DiscardedAt = table.Column<DateTime>(type: "datetime2", nullable: true),
                    Name = table.Column<string>(type: "nvarchar(max)", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Region", x => x.Id);
                });

            migrationBuilder.InsertData(
                table: "Region",
                columns: new[] { "Id", "Name", "CreatedAt", "UpdatedAt" },
                values: new object[,]
                {
                    {1, "Barwon", DateTime.UtcNow, DateTime.UtcNow },
                    {2, "Bayside Peninsula", DateTime.UtcNow, DateTime.UtcNow },
                    {3, "Brimbank Melton", DateTime.UtcNow, DateTime.UtcNow },
                    {4, "Central Highlands", DateTime.UtcNow, DateTime.UtcNow },
                    {5, "Inner-Eastern Melbourne", DateTime.UtcNow, DateTime.UtcNow },
                    {6, "Outer-Eastern Melbourne", DateTime.UtcNow, DateTime.UtcNow },
                    {7, "Inner-Gippsland", DateTime.UtcNow, DateTime.UtcNow },
                    {8, "Outer-Gippsland", DateTime.UtcNow, DateTime.UtcNow },
                    {9, "Goulburn", DateTime.UtcNow, DateTime.UtcNow },
                    {10, "Hume Merri-Bek", DateTime.UtcNow, DateTime.UtcNow },
                    {11, "Loddon", DateTime.UtcNow, DateTime.UtcNow },
                    {12, "Mallee", DateTime.UtcNow, DateTime.UtcNow },
                    {13, "Melbourne CBD", DateTime.UtcNow, DateTime.UtcNow },
                    {14, "North-Eastern Melbourne", DateTime.UtcNow, DateTime.UtcNow },
                    {15, "Ovens Murray", DateTime.UtcNow, DateTime.UtcNow },
                    {16, "Southern Melbourne", DateTime.UtcNow, DateTime.UtcNow },
                    {17, "Outer-Western District", DateTime.UtcNow, DateTime.UtcNow },
                    {18, "Western Melbourne", DateTime.UtcNow, DateTime.UtcNow }
                });

            migrationBuilder.CreateTable(
                name: "AccommodationDuration",
                columns: table => new
                {
                    AccommodationsId = table.Column<long>(type: "bigint", nullable: false),
                    DurationId = table.Column<int>(type: "int", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_AccommodationDuration", x => new { x.AccommodationsId, x.DurationId });
                    table.ForeignKey(
                        name: "FK_AccommodationDuration_Accommodations_AccommodationsId",
                        column: x => x.AccommodationsId,
                        principalTable: "Accommodations",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_AccommodationDuration_Duration_DurationId",
                        column: x => x.DurationId,
                        principalTable: "Duration",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateIndex(
                name: "IX_Amenities_AmenityTypeId",
                table: "Amenities",
                column: "AmenityTypeId");

            migrationBuilder.CreateIndex(
                name: "IX_Accommodations_AccommodationTypeId",
                table: "Accommodations",
                column: "AccommodationTypeId");

            migrationBuilder.CreateIndex(
                name: "IX_Accommodations_DensityId",
                table: "Accommodations",
                column: "DensityId");

            migrationBuilder.CreateIndex(
                name: "IX_Accommodations_RegionId",
                table: "Accommodations",
                column: "RegionId");

            migrationBuilder.CreateIndex(
                name: "IX_AccommodationDuration_DurationId",
                table: "AccommodationDuration",
                column: "DurationId");

            // Data migration: Update existing records to use valid foreign key values
            // Set any invalid AccommodationTypeId values to 1 (Hotel) as default
            migrationBuilder.Sql(@"
                UPDATE Accommodations
                SET AccommodationTypeId = CASE
                                              WHEN AccommodationTypeId IS NULL THEN 1
                                              ELSE AccommodationTypeId + 1
                                          END");

            // Set any invalid RegionId values to 1 (Barwon) as default
            migrationBuilder.Sql(@"
                UPDATE Accommodations
                SET RegionId = CASE
                                   WHEN RegionId IS NULL THEN 1
                                   ELSE RegionId + 1
                               END");

            // Set any invalid DensityId values to 1 (Low) as default
            migrationBuilder.Sql(@"
                UPDATE Accommodations
                SET DensityId = CASE
                                    WHEN DensityId IS NULL THEN 1
                                    ELSE DensityId + 1
                                END");

            // Set any invalid AmenityTypeId values to 1 (Characteristic) as default
            migrationBuilder.Sql(@"
                UPDATE Amenities
                SET AmenityTypeId = CASE
                                        WHEN AmenityTypeId IS NULL THEN 1
                                        ELSE AmenityTypeId + 1
                                    END");

            migrationBuilder.AddForeignKey(
                name: "FK_Accommodations_AccommodationType_AccommodationTypeId",
                table: "Accommodations",
                column: "AccommodationTypeId",
                principalTable: "AccommodationType",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_Accommodations_Density_DensityId",
                table: "Accommodations",
                column: "DensityId",
                principalTable: "Density",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_Accommodations_Region_RegionId",
                table: "Accommodations",
                column: "RegionId",
                principalTable: "Region",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_Amenities_AmenityType_AmenityTypeId",
                table: "Amenities",
                column: "AmenityTypeId",
                principalTable: "AmenityType",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_Accommodations_AccommodationType_AccommodationTypeId",
                table: "Accommodations");

            migrationBuilder.DropForeignKey(
                name: "FK_Accommodations_Density_DensityId",
                table: "Accommodations");

            migrationBuilder.DropForeignKey(
                name: "FK_Accommodations_Region_RegionId",
                table: "Accommodations");

            migrationBuilder.DropForeignKey(
                name: "FK_Amenities_AmenityType_AmenityTypeId",
                table: "Amenities");

            migrationBuilder.DropTable(
                name: "AccommodationDuration");

            migrationBuilder.DropTable(
                name: "AccommodationType");

            migrationBuilder.DropTable(
                name: "AmenityType");

            migrationBuilder.DropTable(
                name: "Density");

            migrationBuilder.DropTable(
                name: "Region");

            migrationBuilder.DropTable(
                name: "Duration");

            migrationBuilder.DropIndex(
                name: "IX_Amenities_AmenityTypeId",
                table: "Amenities");

            migrationBuilder.DropIndex(
                name: "IX_Accommodations_AccommodationTypeId",
                table: "Accommodations");

            migrationBuilder.DropIndex(
                name: "IX_Accommodations_DensityId",
                table: "Accommodations");

            migrationBuilder.DropIndex(
                name: "IX_Accommodations_RegionId",
                table: "Accommodations");

            migrationBuilder.RenameColumn(
                name: "AmenityTypeId",
                table: "Amenities",
                newName: "AmenityType");

            migrationBuilder.RenameColumn(
                name: "RegionId",
                table: "Accommodations",
                newName: "Region");

            migrationBuilder.RenameColumn(
                name: "DensityId",
                table: "Accommodations",
                newName: "Density");

            migrationBuilder.RenameColumn(
                name: "AccommodationTypeId",
                table: "Accommodations",
                newName: "AccommodationType");

            migrationBuilder.AddColumn<string>(
                name: "Duration",
                table: "Accommodations",
                type: "nvarchar(max)",
                nullable: false,
                defaultValue: "");
        }
    }
}
