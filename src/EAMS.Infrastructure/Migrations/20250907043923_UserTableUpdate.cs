﻿using Microsoft.EntityFrameworkCore.Migrations;
using NetTopologySuite.Geometries;

#nullable disable

namespace EAMS.Infrastructure.Migrations
{
    /// <inheritdoc />
    public partial class UserTableUpdate : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_Amenities_Accommodations_AccommodationId",
                table: "Amenities");

            migrationBuilder.DropForeignKey(
                name: "FK_AmenityOptions_Amenities_AmenityId",
                table: "AmenityOptions");

            migrationBuilder.DropIndex(
                name: "IX_Amenities_AccommodationId",
                table: "Amenities");

            migrationBuilder.DropColumn(
                name: "AccommodationId",
                table: "Amenities");

            migrationBuilder.RenameColumn(
                name: "Street",
                table: "Accommodations",
                newName: "StreetLine1");

            migrationBuilder.AlterColumn<long>(
                name: "AmenityId",
                table: "AmenityOptions",
                type: "bigint",
                nullable: false,
                defaultValue: 0L,
                oldClrType: typeof(long),
                oldType: "bigint",
                oldNullable: true);

            migrationBuilder.AlterColumn<string>(
                name: "Duration",
                table: "Accommodations",
                type: "nvarchar(max)",
                nullable: false,
                oldClrType: typeof(int),
                oldType: "int");

            migrationBuilder.AddColumn<DateTime>(
                name: "DiscardedAt",
                table: "Accommodations",
                type: "datetime2",
                nullable: true);

            migrationBuilder.AddColumn<Point>(
                name: "Location",
                table: "Accommodations",
                type: "geography",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "StreetLine2",
                table: "Accommodations",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.CreateTable(
                name: "AccommodationAmenities",
                columns: table => new
                {
                    AccommodationsId = table.Column<long>(type: "bigint", nullable: false),
                    AmenityOptionsId = table.Column<long>(type: "bigint", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_AccommodationAmenities", x => new { x.AccommodationsId, x.AmenityOptionsId });
                    table.ForeignKey(
                        name: "FK_AccommodationAmenities_Accommodations_AccommodationsId",
                        column: x => x.AccommodationsId,
                        principalTable: "Accommodations",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_AccommodationAmenities_AmenityOptions_AmenityOptionsId",
                        column: x => x.AmenityOptionsId,
                        principalTable: "AmenityOptions",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "Organisations",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    Name = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    ParentOrgId = table.Column<Guid>(type: "uniqueidentifier", nullable: true),
                    StreetLine1 = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    StreetLine2 = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    State = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    Postcode = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    Suburb = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    Service = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    ClientGroup = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    KeyContactName = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    KeyContactPhone = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    KeyContactEmail = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "datetime2", nullable: false),
                    UpdatedAt = table.Column<DateTime>(type: "datetime2", nullable: false),
                    DiscardedAt = table.Column<DateTime>(type: "datetime2", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Organisations", x => x.Id);
                    table.ForeignKey(
                        name: "FK_Organisations_Organisations_ParentOrgId",
                        column: x => x.ParentOrgId,
                        principalTable: "Organisations",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "OrgAccommodation",
                columns: table => new
                {
                    AccommodationsId = table.Column<long>(type: "bigint", nullable: false),
                    OrganisationsId = table.Column<Guid>(type: "uniqueidentifier", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_OrgAccommodation", x => new { x.AccommodationsId, x.OrganisationsId });
                    table.ForeignKey(
                        name: "FK_OrgAccommodation_Accommodations_AccommodationsId",
                        column: x => x.AccommodationsId,
                        principalTable: "Accommodations",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_OrgAccommodation_Organisations_OrganisationsId",
                        column: x => x.OrganisationsId,
                        principalTable: "Organisations",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "Users",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    OrganisationId = table.Column<Guid>(type: "uniqueidentifier", nullable: true),
                    Position = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    CreatedAt = table.Column<DateTime>(type: "datetime2", nullable: false),
                    UpdatedAt = table.Column<DateTime>(type: "datetime2", nullable: false),
                    DiscardedAt = table.Column<DateTime>(type: "datetime2", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Users", x => x.Id);
                    table.ForeignKey(
                        name: "FK_Users_Organisations_OrganisationId",
                        column: x => x.OrganisationId,
                        principalTable: "Organisations",
                        principalColumn: "Id");
                });

            migrationBuilder.CreateTable(
                name: "UserInvitations",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    InvitationId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    InvitedUserId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    InvitedByUserId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    TargetOrganisationId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    EntraResponse = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    InviteRedirectUrl = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    UserId = table.Column<Guid>(type: "uniqueidentifier", nullable: true),
                    CreatedAt = table.Column<DateTime>(type: "datetime2", nullable: false),
                    UpdatedAt = table.Column<DateTime>(type: "datetime2", nullable: false),
                    DiscardedAt = table.Column<DateTime>(type: "datetime2", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_UserInvitations", x => x.Id);
                    table.ForeignKey(
                        name: "FK_UserInvitations_Organisations_TargetOrganisationId",
                        column: x => x.TargetOrganisationId,
                        principalTable: "Organisations",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_UserInvitations_Users_InvitedByUserId",
                        column: x => x.InvitedByUserId,
                        principalTable: "Users",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_UserInvitations_Users_UserId",
                        column: x => x.UserId,
                        principalTable: "Users",
                        principalColumn: "Id");
                });

            migrationBuilder.CreateIndex(
                name: "IX_AccommodationAmenities_AmenityOptionsId",
                table: "AccommodationAmenities",
                column: "AmenityOptionsId");

            migrationBuilder.CreateIndex(
                name: "IX_OrgAccommodation_OrganisationsId",
                table: "OrgAccommodation",
                column: "OrganisationsId");

            migrationBuilder.CreateIndex(
                name: "IX_Organisations_ParentOrgId",
                table: "Organisations",
                column: "ParentOrgId");

            migrationBuilder.CreateIndex(
                name: "IX_UserInvitations_InvitedByUserId",
                table: "UserInvitations",
                column: "InvitedByUserId");

            migrationBuilder.CreateIndex(
                name: "IX_UserInvitations_TargetOrganisationId",
                table: "UserInvitations",
                column: "TargetOrganisationId");

            migrationBuilder.CreateIndex(
                name: "IX_UserInvitations_UserId",
                table: "UserInvitations",
                column: "UserId");

            migrationBuilder.CreateIndex(
                name: "IX_Users_OrganisationId",
                table: "Users",
                column: "OrganisationId");

            migrationBuilder.AddForeignKey(
                name: "FK_AmenityOptions_Amenities_AmenityId",
                table: "AmenityOptions",
                column: "AmenityId",
                principalTable: "Amenities",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_AmenityOptions_Amenities_AmenityId",
                table: "AmenityOptions");

            migrationBuilder.DropTable(
                name: "AccommodationAmenities");

            migrationBuilder.DropTable(
                name: "OrgAccommodation");

            migrationBuilder.DropTable(
                name: "UserInvitations");

            migrationBuilder.DropTable(
                name: "Users");

            migrationBuilder.DropTable(
                name: "Organisations");

            migrationBuilder.DropColumn(
                name: "DiscardedAt",
                table: "Accommodations");

            migrationBuilder.DropColumn(
                name: "Location",
                table: "Accommodations");

            migrationBuilder.DropColumn(
                name: "StreetLine2",
                table: "Accommodations");

            migrationBuilder.RenameColumn(
                name: "StreetLine1",
                table: "Accommodations",
                newName: "Street");

            migrationBuilder.AlterColumn<long>(
                name: "AmenityId",
                table: "AmenityOptions",
                type: "bigint",
                nullable: true,
                oldClrType: typeof(long),
                oldType: "bigint");

            migrationBuilder.AddColumn<long>(
                name: "AccommodationId",
                table: "Amenities",
                type: "bigint",
                nullable: true);

            migrationBuilder.AlterColumn<int>(
                name: "Duration",
                table: "Accommodations",
                type: "int",
                nullable: false,
                oldClrType: typeof(string),
                oldType: "nvarchar(max)");

            migrationBuilder.CreateIndex(
                name: "IX_Amenities_AccommodationId",
                table: "Amenities",
                column: "AccommodationId");

            migrationBuilder.AddForeignKey(
                name: "FK_Amenities_Accommodations_AccommodationId",
                table: "Amenities",
                column: "AccommodationId",
                principalTable: "Accommodations",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_AmenityOptions_Amenities_AmenityId",
                table: "AmenityOptions",
                column: "AmenityId",
                principalTable: "Amenities",
                principalColumn: "Id");
        }
    }
}
