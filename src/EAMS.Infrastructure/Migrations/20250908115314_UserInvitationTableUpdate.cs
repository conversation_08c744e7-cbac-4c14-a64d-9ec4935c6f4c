﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace EAMS.Infrastructure.Migrations
{
    /// <inheritdoc />
    public partial class UserInvitationTableUpdate : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_UserInvitations_Organisations_TargetOrganisationId",
                table: "UserInvitations");

            migrationBuilder.DropForeignKey(
                name: "FK_UserInvitations_Users_InvitedByUserId",
                table: "UserInvitations");

            migrationBuilder.DropForeignKey(
                name: "FK_UserInvitations_Users_UserId",
                table: "UserInvitations");

            migrationBuilder.DropIndex(
                name: "IX_UserInvitations_UserId",
                table: "UserInvitations");

            migrationBuilder.DropColumn(
                name: "DiscardedAt",
                table: "UserInvitations");

            migrationBuilder.DropColumn(
                name: "EntraResponse",
                table: "UserInvitations");

            migrationBuilder.DropColumn(
                name: "InvitationId",
                table: "UserInvitations");

            migrationBuilder.DropColumn(
                name: "UserId",
                table: "UserInvitations");

            migrationBuilder.AlterColumn<Guid>(
                name: "TargetOrganisationId",
                table: "UserInvitations",
                type: "uniqueidentifier",
                nullable: true,
                oldClrType: typeof(Guid),
                oldType: "uniqueidentifier");

            migrationBuilder.AlterColumn<string>(
                name: "InviteRedirectUrl",
                table: "UserInvitations",
                type: "nvarchar(max)",
                nullable: true,
                oldClrType: typeof(string),
                oldType: "nvarchar(max)");

            migrationBuilder.AddColumn<string>(
                name: "Roles",
                table: "UserInvitations",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddForeignKey(
                name: "FK_UserInvitations_Organisations_TargetOrganisationId",
                table: "UserInvitations",
                column: "TargetOrganisationId",
                principalTable: "Organisations",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_UserInvitations_Users_InvitedByUserId",
                table: "UserInvitations",
                column: "InvitedByUserId",
                principalTable: "Users",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_UserInvitations_Organisations_TargetOrganisationId",
                table: "UserInvitations");

            migrationBuilder.DropForeignKey(
                name: "FK_UserInvitations_Users_InvitedByUserId",
                table: "UserInvitations");

            migrationBuilder.DropColumn(
                name: "Roles",
                table: "UserInvitations");

            migrationBuilder.AlterColumn<Guid>(
                name: "TargetOrganisationId",
                table: "UserInvitations",
                type: "uniqueidentifier",
                nullable: false,
                defaultValue: new Guid("00000000-0000-0000-0000-000000000000"),
                oldClrType: typeof(Guid),
                oldType: "uniqueidentifier",
                oldNullable: true);

            migrationBuilder.AlterColumn<string>(
                name: "InviteRedirectUrl",
                table: "UserInvitations",
                type: "nvarchar(max)",
                nullable: false,
                defaultValue: "",
                oldClrType: typeof(string),
                oldType: "nvarchar(max)",
                oldNullable: true);

            migrationBuilder.AddColumn<DateTime>(
                name: "DiscardedAt",
                table: "UserInvitations",
                type: "datetime2",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "EntraResponse",
                table: "UserInvitations",
                type: "nvarchar(max)",
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<Guid>(
                name: "InvitationId",
                table: "UserInvitations",
                type: "uniqueidentifier",
                nullable: false,
                defaultValue: new Guid("00000000-0000-0000-0000-000000000000"));

            migrationBuilder.AddColumn<Guid>(
                name: "UserId",
                table: "UserInvitations",
                type: "uniqueidentifier",
                nullable: true);

            migrationBuilder.CreateIndex(
                name: "IX_UserInvitations_UserId",
                table: "UserInvitations",
                column: "UserId");

            migrationBuilder.AddForeignKey(
                name: "FK_UserInvitations_Organisations_TargetOrganisationId",
                table: "UserInvitations",
                column: "TargetOrganisationId",
                principalTable: "Organisations",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_UserInvitations_Users_InvitedByUserId",
                table: "UserInvitations",
                column: "InvitedByUserId",
                principalTable: "Users",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_UserInvitations_Users_UserId",
                table: "UserInvitations",
                column: "UserId",
                principalTable: "Users",
                principalColumn: "Id");
        }
    }
}
