using EAMS.Domain.Aggregates;
using EAMS.Domain.Repositories;
using EAMS.Infrastructure.Data;

namespace EAMS.Infrastructure.Repositories;

public class OrganisationRepository : Repository<Organisation, Guid>, IOrganisationRepository
{
    public OrganisationRepository(EamsDbContext context) : base(context)
    {
    }

    public async Task<Organisation?> GetOrganisationByNameAsync(string name)
    {
        var foundOrganisations = await this.GetAllAsync(where: o => o.Name == name);
        return foundOrganisations.FirstOrDefault();
    }
}
