using System.Linq.Expressions;
using EAMS.Domain.Entities;
using EAMS.Domain.Interfaces;
using Moq;
using Microsoft.Graph.Models;
using GraphUser = Microsoft.Graph.Models.User;
using User = EAMS.Domain.Aggregates.User;
using Microsoft.Extensions.Logging;
using System.Text.Json;
using Microsoft.Extensions.Configuration;
using EAMS.Domain.Repositories;
using EAMS.Domain.Aggregates;
using EAMS.Application.Services;
using AutoMapper;
using EAMS.API.Mappings;
using EAMS.Application.DTOs;

namespace EAMS.Tests.ApplicationServiceTests;

public class UserServicesTestFixture
{
    private Mock<IGraphService> _mockGraphService;
    private Mock<IUserRepository> _mockUserRepository;
    private Mock<IUserInvitationRepository> _mockUserInvitationRepository;
    private Mock<IOrganisationRepository> _mockOrganisationRepository;
    private Mock<IOrganisationDomainService> _mockOrgDomainService;
    private Mock<ILogger<UserService>> _mockLogger;
    private Mock<IConfiguration> _mockConfiguration;
    private GraphUser _user;
    private Group _usersGroup;
    private Organisation _organisation;
    private UserService _targetService;
    private IMapper _mapper;

    public UserServicesTestFixture()
    {
        // Mock GraphServiceClient
        _mockGraphService = new Mock<IGraphService>();
        _user = new GraphUser()
        {
            Id = Guid.NewGuid().ToString(),
            DisplayName = "John Doe",
            GivenName = "John",
            Surname = "Doe",
            Mail = "<EMAIL>",
            CompanyName = "McAuleys",
            JobTitle = "Developer",
            MobilePhone = "0411 223 345",
        };
        _mockGraphService
            .Setup(x => x.GetCurrentLoginUserAsync())
            .ReturnsAsync(_user);

        _usersGroup = new Group()
        {
            Id = Guid.NewGuid().ToString(),
            DisplayName = "Users"
        };

        _mockGraphService
            .Setup(x => x.GetGroupByNameAsync("Users"))
            .ReturnsAsync(_usersGroup);

        // Mock Dependencies
        _mockUserRepository = new Mock<IUserRepository>();
        _mockUserInvitationRepository = new Mock<IUserInvitationRepository>();
        _mockOrganisationRepository = new Mock<IOrganisationRepository>();
        _mockLogger = new Mock<ILogger<UserService>>();
        _mockConfiguration = new Mock<IConfiguration>();
        _mockOrgDomainService = new Mock<IOrganisationDomainService>();

        // Create Mapper
        var mapperConfig = new MapperConfiguration(cfg =>
        {
            cfg.AddProfile<AccessProfile>();
            cfg.AddProfile<AccommodationProfile>();
        });
        _mapper = mapperConfig.CreateMapper();

        // Setup Mock Organisation
        _organisation = new Organisation()
        {
            Id = Guid.NewGuid(),
            Name = "McAuleys",
        };

        _mockOrganisationRepository
            .Setup(x => x.GetByIdAsync(_organisation.Id))
            .ReturnsAsync(_organisation);

        // Setup Mock Configuration
        _mockConfiguration.Setup(config => config.GetSection("GraphApi:RolesMapping:Users").Value)
            .Returns("Users");

        _targetService = new UserService(_mockGraphService.Object,
            _mockUserRepository.Object,
            _mockUserInvitationRepository.Object,
            _mockOrganisationRepository.Object,
            _mockOrgDomainService.Object,
            _mockConfiguration.Object,
            _mapper,
            _mockLogger.Object);
    }

    [Fact]
    public async Task Test_GetCurrentLoginUserAsync_Should_Retrieve_Additional_Data_From_UserRepo()
    {
        // arrange
        var dbUser = new User()
        {
            Id = Guid.Parse(_user.Id),
        };
        _mockUserRepository
            .Setup(x => x.GetByIdAsync(It.IsAny<Guid>())).ReturnsAsync(dbUser);


        // act
        var result = await _targetService.GetCurrentLoginUserAsync();

        // assert
        _mockUserRepository.Verify(repo => repo.GetByIdAsync(It.IsAny<Guid>(), usr => usr.Organisation), Times.Once);
        Assert.Equal(result.Id, Guid.Parse(_user.Id));
        Assert.Equal(result.DisplayName, _user.DisplayName);
        Assert.Equal(result.GivenName, _user.GivenName);
        Assert.Equal(result.Surname, _user.Surname);
        Assert.Equal(result.Email, _user.Mail);
        Assert.Equal(result.CompanyName, _user.CompanyName);
    }

    [Fact]
    public async Task Test_GetCurrentLoginUserAsync_UserDoesNotExistsInUserTable_Should_Check_Existing_Invitation_AndCreate_NewUserRecord()
    {
        // arrange
        _mockUserInvitationRepository
            .Setup(repo => repo.GetAllAsync(It.IsAny<Expression<Func<UserInvitation, bool>>>(),
                It.IsAny<Expression<Func<UserInvitation, object>>>()))
            .ReturnsAsync(new List<UserInvitation>() {new UserInvitation()
            {
                Id = Guid.NewGuid(),
                TargetOrganisationId = _organisation.Id,
                TargetOrganisation = _organisation,
            }});

        // act
        var result = await _targetService.GetCurrentLoginUserAsync();

        // assert
        Assert.NotNull(result);
        Assert.Equal(_organisation.Name, result.CompanyName);
        _mockUserRepository.Verify(repo => repo.AddAsync(It.IsAny<User>()), Times.Once);
    }

    [Fact]
    public async Task Test_CreateInvitationAsync_Should_AddUserToGroup_CreateNewUserInvitation_ReturnsValidId()
    {
        // arrange
        var userInvitation = new InvitationDto()
        {
            InvitedUserEmailAddress = "<EMAIL>",
            TargetOrganisationId = _organisation.Id,
            Roles = "Users"
        };

        Invitation mockInvitation = new Invitation()
        {
            Id = Guid.NewGuid().ToString(),
            InvitedUser = new GraphUser()
            {
                Id = Guid.NewGuid().ToString()
            }
        };

        _mockGraphService
            .Setup(x => x.InviteUserAsync(userInvitation.InvitedUserEmailAddress, It.IsAny<string>()))
            .ReturnsAsync(mockInvitation);

        // act
        var result = await _targetService.CreateInvitationAsync(userInvitation);

        // assert
        _mockUserInvitationRepository.Verify(repo => repo.AddAsync(It.IsAny<UserInvitation>()), Times.Once);
        _mockGraphService.Verify(repo => repo.GetCurrentLoginUserAsync(), Times.Once);
        _mockGraphService.Verify(repo => repo.GetGroupByNameAsync(userInvitation.Roles), Times.Once);
        _mockGraphService.Verify(repo => repo.AddUserToGroupAsync(Guid.Parse(mockInvitation.InvitedUser.Id),
            Guid.Parse(_usersGroup.Id)), Times.Once);
    }

    [Fact]
    public async Task Test_CreateInvitationAsync_WithExistingInvitation_ShouldResendInvitation_AndUpdateInvitationRecord()
    {
        // arrange
        GraphUser userToInvite = new GraphUser()
        {
            Id = Guid.NewGuid().ToString(),
            Mail = "<EMAIL>"
        };

        UserInvitation existingInvitation = new UserInvitation()
        {
            Id = Guid.NewGuid(),
            TargetOrganisationId = _organisation.Id,
            InvitedByUserId = Guid.Parse(_user.Id),
            InvitedUserId = Guid.Parse(userToInvite.Id),
            InvitedUserEmailAddress = userToInvite.Mail,
        };

        InvitationDto dto = new InvitationDto
        {
            InvitedUserEmailAddress = userToInvite.Mail,
            TargetOrganisationId = _organisation.Id,
            Roles = "Users"
        };

        Invitation mockInvitation = new Invitation()
        {
            Id = existingInvitation.Id.ToString(),
            InvitedUser = new GraphUser()
            {
                Id = userToInvite.Id
            }
        };

        _mockUserInvitationRepository.Setup(repo => repo.GetByIdAsync(It.IsAny<Guid>()))
            .ReturnsAsync(existingInvitation);

        _mockGraphService
            .Setup(x => x.InviteUserAsync(userToInvite.Mail, It.IsAny<string>()))
            .ReturnsAsync(mockInvitation);

        // act
        var result = await _targetService.CreateInvitationAsync(dto);

        // assert
        _mockUserInvitationRepository.Verify(repo => repo.UpdateAsync(It.IsAny<UserInvitation>()), Times.Once);
        _mockGraphService.Verify(repo => repo.AddUserToGroupAsync(It.IsAny<Guid>(), It.IsAny<Guid>()), Times.Never);
    }

    [Fact]
    public async Task Test_UpdateUserDetailsAsync_Should_Call_UserRepo_Update_When_OrganisationChanges()
    {
        // arrange
        var updatedDetails = JsonSerializer.Deserialize<GraphUser>(
            JsonSerializer.Serialize(_user));

        updatedDetails.CompanyName = "New Company";
        updatedDetails.DisplayName = "John Updated";
        updatedDetails.Surname = "Updated";

        var userDto = _mapper.Map<UserDto>(new User
        {
            Id = Guid.Parse(_user.Id),
            GraphUser = updatedDetails
        });

        _mockGraphService.Setup(s => s.PatchUserDetailsAsync(It.IsAny<GraphUser>()))
            .ReturnsAsync(updatedDetails);

        _mockOrganisationRepository.Setup(repo => repo.GetOrganisationByNameAsync(updatedDetails.CompanyName))
            .ReturnsAsync(new Organisation()
            {
                Id = Guid.NewGuid(),
                Name = updatedDetails.CompanyName
            });

        _mockUserRepository.Setup(repo => repo.GetByIdAsync(It.IsAny<Guid>()))
            .ReturnsAsync(new User()
            {
                Id = Guid.Parse(_user.Id),
                OrganisationId = _organisation.Id,
                Organisation = _organisation,
            });

        // act
        await _targetService.UpdateUserDetailsAsync(userDto);

        // assert
        _mockGraphService.Verify(svc => svc.PatchUserDetailsAsync(It.IsAny<GraphUser>()), Times.Once);
        _mockUserRepository.Verify(repo => repo.UpdateAsync(It.IsAny<User>()), Times.Once);
    }

    [Fact]
    public async Task Test_GetUserByIdAsync_Should_Return_Null_When_GraphUser_NotFound()
    {
        // arrange
        _mockGraphService.Setup(svc => svc.GetUserByIdAsync(It.IsAny<Guid>()))
            .ReturnsAsync((GraphUser)null);

        // act
        var result = await _targetService.GetUserByIdAsync(Guid.NewGuid());

        // assert
        Assert.Null(result);
        _mockGraphService.Verify(svc => svc.GetUserByIdAsync(It.IsAny<Guid>()), Times.Once);
        _mockUserRepository.Verify(svc => svc.GetByIdAsync(It.IsAny<Guid>(), usr => usr.Organisation), Times.Never);
    }

    [Fact]
    public async Task Test_GetUserByIdAsync_Should_Return_Null_When_UserRecord_NotFound()
    {
        // arrange
        _mockGraphService.Setup(svc => svc.GetUserByIdAsync(It.IsAny<Guid>()))
            .ReturnsAsync(_user);
        _mockUserRepository.Setup(repo => repo.GetByIdAsync(It.IsAny<Guid>(), usr => usr.Organisation))
            .ReturnsAsync((User)null);

        // act 
        var result = await _targetService.GetUserByIdAsync(Guid.Parse(_user.Id));

        // assert
        Assert.Null(result);
        _mockGraphService.Verify(svc => svc.GetUserByIdAsync(It.IsAny<Guid>()), Times.Once);
        _mockUserRepository.Verify(svc => svc.GetByIdAsync(It.IsAny<Guid>(), usr => usr.Organisation), Times.Once);
    }

    [Fact]
    public async Task Test_AddUserToGroupAsync_Should_Call_GraphService_AddUserToGroupAsync()
    {
        // arrange
        _mockGraphService.Setup(svc => svc.GetUserByIdAsync(Guid.Parse(_user.Id)))
            .ReturnsAsync(_user);
        _mockGraphService.Setup(svc => svc.GetGroupByNameAsync(_usersGroup.DisplayName))
            .ReturnsAsync(_usersGroup);
        _mockGraphService.Setup(svc => svc.GetCurrentLoginUserAsync())
            .ReturnsAsync(new GraphUser
            {
                Id = Guid.NewGuid().ToString(),
                DisplayName = "Admin User",
            });

        // act
        await _targetService.AddUserToGroupAsync(Guid.Parse(_user.Id), _usersGroup.DisplayName);

        // assert
        _mockGraphService.Verify(svc => svc.AddUserToGroupAsync(Guid.Parse(_user.Id), Guid.Parse(_usersGroup.Id)), Times.Once);
    }

    [Fact]
    public async Task Test_AddUserToGroupAsync_Should_ThrowError_WhenUser_NotFound()
    {
        // arrange
        _mockGraphService.Setup(svc => svc.GetUserByEmailAsync(_user.Mail))
            .ReturnsAsync((GraphUser)null);
        _mockGraphService.Setup(svc => svc.GetGroupByNameAsync(_usersGroup.DisplayName))
            .ReturnsAsync(_usersGroup);
        _mockGraphService.Setup(svc => svc.GetCurrentLoginUserAsync())
            .ReturnsAsync(new GraphUser
            {
                Id = Guid.NewGuid().ToString(),
                DisplayName = "Admin User",
            });

        // act & assert
        Assert.ThrowsAsync<InvalidDataException>(() => _targetService.AddUserToGroupAsync(Guid.Parse(_user.Id), _usersGroup.DisplayName));
    }

    [Fact]
    public async Task Test_AddUserToGroupAsync_Should_ThrowError_WhenGroup_NotFound()
    {
        // arrange
        _mockGraphService.Setup(svc => svc.GetUserByEmailAsync(_user.Mail))
            .ReturnsAsync(_user);
        _mockGraphService.Setup(svc => svc.GetGroupByNameAsync(_usersGroup.DisplayName))
            .ReturnsAsync((Group)null);
        _mockGraphService.Setup(svc => svc.GetCurrentLoginUserAsync())
            .ReturnsAsync(new GraphUser
            {
                Id = Guid.NewGuid().ToString(),
                DisplayName = "Admin User",
            });

        // act & assert
        Assert.ThrowsAsync<InvalidDataException>(() => _targetService.AddUserToGroupAsync(Guid.Parse(_user.Id), _usersGroup.DisplayName));
    }
}