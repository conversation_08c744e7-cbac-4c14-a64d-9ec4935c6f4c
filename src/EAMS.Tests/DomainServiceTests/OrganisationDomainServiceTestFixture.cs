﻿using EAMS.Domain.Aggregates;
using EAMS.Domain.Interfaces;
using EAMS.Domain.Entities;
using EAMS.Domain.Repositories;
using Moq;
using GraphUser = Microsoft.Graph.Models.User;
using User = EAMS.Domain.Aggregates.User;
using System.Collections.Generic;
using EAMS.Domain.Services;
using System.Linq.Expressions;
namespace EAMS.Tests.DomainServiceTests;

public class OrganisationDomainServiceTestFixture
{
    private readonly Mock<IOrganisationRepository> _mockOrganisationRepository;
    private readonly Mock<IUserRepository> _mockUserRepository;
    private readonly Mock<IGraphService> _mockGraphService;
    private GraphUser _user;
    private Organisation _organisation;
    private User _userRecord;
    private OrganisationDomainService _service;
    public OrganisationDomainServiceTestFixture()
    {
        // Mock dependencies
        _mockOrganisationRepository = new Mock<IOrganisationRepository>();
        _mockUserRepository = new Mock<IUserRepository>();
        _mockGraphService = new Mock<IGraphService>();

        _user = new GraphUser()
        {
            Id = Guid.NewGuid().ToString(),
            DisplayName = "John <PERSON>e",
            GivenName = "John",
            Surname = "Doe",
            Mail = "<EMAIL>",
            CompanyName = "McAuleys",
            JobTitle = "Developer",
            MobilePhone = "0411 223 345",
        };

        _organisation = new Organisation
        {
            Id = Guid.NewGuid(),
            Name = "McAuleys",
            StreetLine1 = "1 Main St, Sydney NSW 2000",
            KeyContactPhone = "02 1234 5678",
        };

        _organisation.ChildOrganisations = new List<Organisation>();
        _organisation.ChildOrganisations.Add(new Organisation
        {
            Id = Guid.NewGuid(),
            Name = "McAuleys - Branch 1",
            StreetLine1 = "2 Main St, Sydney NSW 2000",
            KeyContactPhone = "02 1234 5678",
        });
        _organisation.ChildOrganisations.Add(new Organisation
        {
            Id = Guid.NewGuid(),
            Name = "McAuleys - Branch 2",
            StreetLine1 = "3 Main St, Sydney NSW 2000",
            KeyContactPhone = "02 1234 5678",
        });

        _userRecord = new User
        {
            CreatedAt = DateTime.UtcNow,
            Id = Guid.Parse(_user.Id),
            OrganisationId = _organisation.Id,
        };

        // Setup default mock behaviors
        _mockGraphService
            .Setup(x => x.GetCurrentLoginUserAsync())
            .ReturnsAsync(_user);


        _mockOrganisationRepository
            .Setup(x => x.GetAllAsync(It.IsAny<Expression<Func<Organisation, bool>>>()))
            .ReturnsAsync(new List<Organisation>{ _organisation });

        _mockUserRepository
            .Setup(x => x.GetByIdAsync(Guid.Parse(_user.Id)))
            .ReturnsAsync(_userRecord);

        _service = new OrganisationDomainService(
            _mockGraphService.Object,
            _mockOrganisationRepository.Object,
            _mockUserRepository.Object);
    }


    [Fact]
    public async Task Test_CanUserManageOrganisationAsync_ReturnsTrue()
    {
        // act
        var result = await _service.CanUserManageOrganisationAsync(_organisation.Id);

        // assert
        Assert.True(result);
    }

    [Fact]
    public async Task Test_CanUserManageOrganisationAsync_ReturnsTrue_WhenPassingChildOrganisationId()
    {

       // act
        var result = await _service.CanUserManageOrganisationAsync(_organisation.ChildOrganisations.First().Id);

        // assert
        Assert.True(result);
    }
}
