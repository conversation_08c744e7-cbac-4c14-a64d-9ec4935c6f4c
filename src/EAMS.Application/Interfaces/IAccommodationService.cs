using EAMS.Application.DTOs;

namespace EAMS.Application.Interfaces;

public interface IAccommodationService
{
    Task<IEnumerable<AccommodationDto>> GetAll();

    Task<AccommodationDto?> GetById(long id);

    Task<AccommodationDto> Create(AccommodationDto accommodationDto);

    Task<AccommodationDto> Update(AccommodationDto accommodationDto);

    Task<bool> Delete(long id);

    Task<(List<AccommodationDto> results, int totalCount)> SearchAccommodationsAsync(AccommodationSearchRequestDto request);
}
