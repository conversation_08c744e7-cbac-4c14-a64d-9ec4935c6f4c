﻿using EAMS.Application.DTOs;
using EAMS.Domain.Aggregates;
using GraphUser = Microsoft.Graph.Models.User;


namespace EAMS.Domain.Interfaces;

public interface IUserService
{
    public Task<UserDto?> GetCurrentLoginUserAsync();
    public Task<UserDto?> GetUserByIdAsync(Guid userId);
    public Task<UserDto?> GetUserByEmailAsync(string email);
    public Task<UserDto?> UpdateUserDetailsAsync(UserDto dto);
    public Task<InvitationDto?> CreateInvitationAsync(InvitationDto dto);
    public Task<(List<UserDto> results, int totalCount)> SearchUsersAsync(
        string? searchTerm, string? searchProperty,
        int? pageNumber, int? pageSize, string sortBy, string sortDirection);
    public Task AddUserToGroupAsync(Guid userId, string groupName);
    public Task RemoveUserFromGroupAsync(Guid userId, string groupName);
    public Task DeleteUserAsync(Guid userId);
    public Task<List<UserDto>> GetAllUsersAsync();
    public Task<bool> CheckIfCurrentUserCanManageTargetUser(Guid targetUserId);
}
