
using EAMS.Application.DTOs;

namespace EAMS.Domain.Interfaces;

public interface IOrganisationService
{
    Task<IEnumerable<OrganisationDetailsDto>> GetAll();
    Task<IEnumerable<OrganisationDetailsDto>> GetSubOrganisations(Guid parentOrgId);
    Task<OrganisationDetailsDto?> GetById(Guid id);
    Task<OrganisationDetailsDto> Create(CreateOrganisationDto organisation);
    Task<OrganisationDetailsDto> Update(UpdateOrganisationDto organisation);
    Task<bool> Delete(Guid id);
    Task<List<UserDto>> GetOrganisationUsers(Guid orgId);
    Task<(List<OrganisationDetailsDto> results, int totalCount)> SearchOrganisationAsync(string? searchTerm, 
        int pageNumber, int pageSize, string sortBy, string sortDirection);

    Task<bool> CheckIfCurrentUserCanManageOrganisation(Guid organisationId);
}
