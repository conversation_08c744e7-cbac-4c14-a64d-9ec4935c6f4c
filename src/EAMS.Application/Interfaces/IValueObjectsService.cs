﻿using EAMS.Application.DTOs;
using EAMS.Domain.Entities;
using EAMS.Domain.ValueObjects;

namespace EAMS.Application.Interfaces
{
    public interface IValueObjectsService
    {
        public Task<IEnumerable<EnumDto>> GetAll<T>() where T : EnumEntity;
        public Task<EnumDto> Create<T>(T entity) where T : EnumEntity;
        public Task<EnumDto> Update<T>(T entity) where T : EnumEntity;
        public Task Delete<T>(int id) where T : EnumEntity;
    }
}
