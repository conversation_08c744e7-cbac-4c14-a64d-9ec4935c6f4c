namespace EAMS.Application.DTOs;

public class UserSearchRequestDto: SearchRequestDto
{
    public string? DisplayName { get; set; }
    public string? Email { get; set; }

    public override bool ValidateQuery()
    {
        var pageValidation =  base.ValidateQuery();
        if (!pageValidation) 
            return false;

        if (this.Sorting is null)
        {
            this.Sorting = new SearchSorting
            {
                OrderBy = nameof(this.DisplayName),
                Direction = "asc"
            };
        }

        return true;
    }
}