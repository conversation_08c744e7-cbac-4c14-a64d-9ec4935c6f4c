﻿namespace EAMS.Application.DTOs
{
    public class SearchResultDto<T> where T : class
    {
        public IEnumerable<T> Data { get; set; }
        public Pagination Pagination { get; set; }
        public SearchSorting Sort { get; set; }

        public SearchResultDto(IEnumerable<T> data,
            int? pageNumber, int? pageSize, int totalRecords,
            string orderBy, string direction)
        {
            this.Data = data;
            if (!pageSize.HasValue)
                pageSize = data.Count();

            double totalPage = ((double) totalRecords / (double)pageSize.Value) ;

            this.Pagination = new Pagination
            {
                Page = pageNumber ?? 1,
                PageSize = pageSize.Value,
                TotalRecords = totalRecords,
                TotalPages = int.Parse(Math.Ceiling(totalPage).ToString())
            };

            this.Sort = new SearchSorting
            {
                OrderBy = orderBy,
                Direction = direction
            };
        }
    }

    public class  SearchSorting
    {
        public string OrderBy { get; set; }
        public string Direction { get; set; }
    }
}
