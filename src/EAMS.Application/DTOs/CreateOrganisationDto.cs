using System.ComponentModel.DataAnnotations;

namespace EAMS.Application.DTOs;

public class CreateOrganisationDto
{
    [Required(ErrorMessage = "Name is required")]
    [StringLength(200, ErrorMessage = "Name cannot exceed 200 characters")]
    public string Name { get; set; } = string.Empty;

    public Guid? ParentOrgId { get; set; }

    [Required(ErrorMessage = "Street is required")]
    [StringLength(200, ErrorMessage = "Street cannot exceed 200 characters")]
    public string StreetLine1 { get; set; } = string.Empty;

    [StringLength(200, ErrorMessage = "Street cannot exceed 200 characters")]
    public string? StreetLine2 { get; set; }

    [Required(ErrorMessage = "State is required")]
    [StringLength(50, ErrorMessage = "State cannot exceed 50 characters")]
    public string State { get; set; } = string.Empty;

    [Required(ErrorMessage = "Postcode is required")]
    [StringLength(10, ErrorMessage = "Postcode cannot exceed 10 characters")]
    [RegularExpression(@"^\d{4}$", ErrorMessage = "Postcode must be 4 digits")]
    public string Postcode { get; set; } = string.Empty;

    [Required(ErrorMessage = "Suburb is required")]
    [StringLength(100, ErrorMessage = "Suburb cannot exceed 100 characters")]
    public string Suburb { get; set; } = string.Empty;

    [StringLength(200, ErrorMessage = "Service cannot exceed 200 characters")]
    public string? Service { get; set; }

    [StringLength(200, ErrorMessage = "Client group cannot exceed 200 characters")]
    public string? ClientGroup { get; set; }

    [StringLength(100, ErrorMessage = "Key contact name cannot exceed 100 characters")]
    public string? KeyContactName { get; set; }

    [Phone(ErrorMessage = "Invalid phone number format")]
    [StringLength(20, ErrorMessage = "Phone cannot exceed 20 characters")]
    public string? KeyContactPhone { get; set; }

    [EmailAddress(ErrorMessage = "Invalid email format")]
    [StringLength(100, ErrorMessage = "Email cannot exceed 100 characters")]
    public string? KeyContactEmail { get; set; }
}
