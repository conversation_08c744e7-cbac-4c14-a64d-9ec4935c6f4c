﻿using AutoMapper;
using EAMS.Application.DTOs;
using EAMS.Application.Interfaces;
using EAMS.Domain.Exceptions;
using EAMS.Domain.Repositories;
using EAMS.Domain.ValueObjects;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace EAMS.Application.Services
{
    public class ValueObjectsService : IValueObjectsService
    {
        private readonly IRepository<AccommodationType, int> _accommodationTypeRepository;
        private readonly IRepository<AmenityType, int> _amenityTypeRepository;
        private readonly IRepository<Density, int> _densityRepository;
        private readonly IRepository<Duration, int> _durationRepository;
        private readonly IRepository<Region, int> _regionRepository;
        private readonly IMapper _mapper;

        public ValueObjectsService(
            IRepository<AccommodationType, int> accommodationTypeRepository,
            IRepository<AmenityType, int> amenityTypeRepository,
            IRepository<Density, int> densityRepository,
            IRepository<Duration, int> durationRepository,
            IRepository<Region, int> regionRepository,
            IMapper mapper
            )
        {
            _accommodationTypeRepository = accommodationTypeRepository;
            _amenityTypeRepository = amenityTypeRepository;
            _densityRepository = densityRepository;
            _durationRepository = durationRepository;
            _regionRepository = regionRepository;
            _mapper = mapper;
        }


        public async Task<EnumDto> Create<T>(T entity) where T : EnumEntity
        {
            switch (typeof(T))
            {
                case Type t when t == typeof(AccommodationType):
                    await _accommodationTypeRepository.AddAsync(entity as AccommodationType);
                    var accommodationType = await _accommodationTypeRepository.GetByIdAsync(entity.Id);
                    return _mapper.Map<EnumDto>(accommodationType);
                case Type t when t == typeof(AmenityType):
                    await _amenityTypeRepository.AddAsync(entity as AmenityType);
                    var amenityType = await _amenityTypeRepository.GetByIdAsync(entity.Id);
                    return _mapper.Map<EnumDto>(amenityType);
                case Type t when t == typeof(Density):
                    await _densityRepository.AddAsync(entity as Density);
                    var density = await _densityRepository.GetByIdAsync(entity.Id);
                    return _mapper.Map<EnumDto>(density);
                case Type t when t == typeof(Duration):
                    await _durationRepository.AddAsync(entity as Duration);
                    var duration = await _durationRepository.GetByIdAsync(entity.Id);
                    return _mapper.Map<EnumDto>(duration);
                case Type t when t == typeof(Region):
                    await _regionRepository.AddAsync(entity as Region);
                    var region = await _regionRepository.GetByIdAsync(entity.Id);
                    return _mapper.Map<EnumDto>(region);
                default:
                    throw new NotSupportedException($"Type {typeof(T).Name} is not supported.");
            }
        }

        public async Task Delete<T>(int id) where T : EnumEntity
        {
            switch (typeof(T))
            {
                case Type t when t == typeof(AccommodationType):
                    if (await _accommodationTypeRepository.GetByIdAsync(id) == null)
                        throw new EntityNotFoundException("AccommodationType", id);

                    await _accommodationTypeRepository.DeleteAsync(id);
                    break;
                case Type t when t == typeof(AmenityType):
                    if (await _amenityTypeRepository.GetByIdAsync(id) == null)
                        throw new EntityNotFoundException("AmenityType", id);
                    await _amenityTypeRepository.DeleteAsync(id);
                    break;
                case Type t when t == typeof(Density):
                    if (await _densityRepository.GetByIdAsync(id) == null)
                        throw new EntityNotFoundException("Density", id);
                    await _densityRepository.DeleteAsync(id);
                    break;
                case Type t when t == typeof(Duration):
                    if (await _durationRepository.GetByIdAsync(id) == null)
                        throw new EntityNotFoundException("Duration", id);
                    await _durationRepository.DeleteAsync(id);
                    break;
                case Type t when t == typeof(Region):
                    if (await _regionRepository.GetByIdAsync(id) == null)
                        throw new EntityNotFoundException("Region", id);
                    await _regionRepository.DeleteAsync(id);
                    break;
                default:
                    throw new NotSupportedException($"Type {typeof(T).Name} is not supported.");
            }
        }

        public async Task<IEnumerable<EnumDto>> GetAll<T>() where T : EnumEntity
        {
            switch (typeof(T))
            {

                case Type t when t == typeof(AccommodationType):
                    var accommodationTypes = await _accommodationTypeRepository.GetAllAsync();
                    return _mapper.Map<IEnumerable<EnumDto>>(accommodationTypes);
                case Type t when t == typeof(AmenityType):
                    var amenityTypes = await _amenityTypeRepository.GetAllAsync();
                    return _mapper.Map<IEnumerable<EnumDto>>(amenityTypes);
                case Type t when t == typeof(Density):
                    var densities = await _densityRepository.GetAllAsync();
                    return _mapper.Map<IEnumerable<EnumDto>>(densities);
                case Type t when t == typeof(Duration):
                    var durations = await _durationRepository.GetAllAsync();
                    return _mapper.Map<IEnumerable<EnumDto>>(durations);
                case Type t when t == typeof(Region):
                    var regions = await _regionRepository.GetAllAsync();
                    return _mapper.Map<IEnumerable<EnumDto>>(regions);
                default:
                    throw new NotSupportedException($"Type {typeof(T).Name} is not supported.");
            }
        }

        public async Task<EnumDto> Update<T>(T entity) where T : EnumEntity
        {
            switch (typeof(T))
            {
                case Type t when t == typeof(AccommodationType):
                    var existingAccommodationType = await _accommodationTypeRepository.GetByIdAsync(entity.Id);
                    if (existingAccommodationType is null)
                    {
                        throw new EntityNotFoundException("AccommodationType", entity.Id);
                    }
                    await _accommodationTypeRepository.UpdateAsync(entity as AccommodationType);
                    var accommodationType = await _accommodationTypeRepository.GetByIdAsync(entity.Id);
                    return _mapper.Map<EnumDto>(accommodationType);
                case Type t when t == typeof(AmenityType):
                    var existingAmenityType = await _amenityTypeRepository.GetByIdAsync(entity.Id);
                    if (existingAmenityType is null)
                    {
                        throw new EntityNotFoundException("AmenityType", entity.Id);
                    }
                    await _amenityTypeRepository.UpdateAsync(entity as AmenityType);
                    var amenityType = await _amenityTypeRepository.GetByIdAsync(entity.Id);
                    return await Task.FromResult(_mapper.Map<EnumDto>(amenityType));
                case Type t when t == typeof(Density):
                    var existingDensity = await _densityRepository.GetByIdAsync(entity.Id);
                    if (existingDensity is null)
                    {
                        throw new EntityNotFoundException("Density", entity.Id);
                    }
                    await _densityRepository.UpdateAsync(entity as Density);
                    var density = await _densityRepository.GetByIdAsync(entity.Id);
                    return await Task.FromResult(_mapper.Map<EnumDto>(density));
                case Type t when t == typeof(Duration):
                    var existingDuration = await _durationRepository.GetByIdAsync(entity.Id);
                    if (existingDuration is null)
                    {
                        throw new EntityNotFoundException("Duration", entity.Id);
                    }
                    await _durationRepository.UpdateAsync(entity as Duration);
                    var duration = await _durationRepository.GetByIdAsync(entity.Id);
                    return await Task.FromResult(_mapper.Map<EnumDto>(duration));
                case Type t when t == typeof(Region):
                    var existingRegion = await _regionRepository.GetByIdAsync(entity.Id);
                    if (existingRegion is null)
                    {
                        throw new EntityNotFoundException("Region", entity.Id);
                    }
                    await _regionRepository.UpdateAsync(entity as Region);
                    var region = await _regionRepository.GetByIdAsync(entity.Id);
                    return _mapper.Map<EnumDto>(region);
                default:
                    throw new NotSupportedException($"Type {typeof(T).Name} is not supported.");
            }
        }
    }
}
