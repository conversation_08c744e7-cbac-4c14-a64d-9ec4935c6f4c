using AutoMapper;
using EAMS.Application.DTOs;
using EAMS.Application.Interfaces;
using EAMS.Domain.Interfaces;
using EAMS.Domain.Aggregates;
using EAMS.Domain.Exceptions;
using EAMS.Domain.Repositories;
using EAMS.Domain.ValueObjects;
using System.Linq;
using System.Linq.Expressions;
using LinqKit;

namespace EAMS.Application.Services;

public class AccommodationService : IAccommodationService
{
    private readonly IAccommodationRepository _accommodationRepository;
    private readonly IMapper _mapper;
    private readonly IValidationService _validationService;
    private readonly IRepository<Duration, int> _durationRepository;
    private readonly IAmenityOptionsRepository _amenityOptionsRepository;

    public AccommodationService(
        IAccommodationRepository accommodationRepository,
        IMapper mapper,
        IValidationService validationService,
        IRepository<Duration, int> durationRepository,
        IAmenityOptionsRepository amenityOptionsRepository)
    {
        _accommodationRepository = accommodationRepository;
        _mapper = mapper;
        _validationService = validationService;
        _durationRepository = durationRepository;
        _amenityOptionsRepository = amenityOptionsRepository;
    }

    public async Task<IEnumerable<AccommodationDto>> GetAll()
    {
        var accommodations = await _accommodationRepository.GetAllAsync();
        return _mapper.Map<IEnumerable<AccommodationDto>>(accommodations);
    }

    public async Task<AccommodationDto?> GetById(long id)
    {
        var accommodation = await _accommodationRepository.GetByIdAsync(id);
        return accommodation != null ? _mapper.Map<AccommodationDto>(accommodation) : null;
    }

    public async Task<AccommodationDto> Create(AccommodationDto accommodationDto)
    {
        // Validate all references using the validation service
        await _validationService.ValidateAccommodationReferencesAsync(
            accommodationDto.RegionId,
            accommodationDto.AccommodationTypeId,
            accommodationDto.DensityId,
            accommodationDto.DurationIds,
            accommodationDto.AmenityIds);

        var accommodation = _mapper.Map<Accommodation>(accommodationDto);

        // Attach existing duration entities so EF Core can populate the join table
        var durations = await _durationRepository.GetAllAsync(d => accommodationDto.DurationIds.Contains(d.Id));
        accommodation.Duration = durations.ToList();

        // Attach amenity options based on provided IDs
        var amenityOptions = accommodationDto.AmenityIds.Any()
            ? await _amenityOptionsRepository.GetAllAsync(ao => accommodationDto.AmenityIds.Contains(ao.AmenityId))
            : Enumerable.Empty<AmenityOptions>();
        accommodation.AmenityOptions = amenityOptions.ToList();

        // Set timestamps for new entity using validation service
        // _validationService.SetTimestampsForCreate(accommodation);

        await _accommodationRepository.AddAsync(accommodation);
        return _mapper.Map<AccommodationDto>(accommodation);
    }

    public async Task<AccommodationDto> Update(AccommodationDto accommodationDto)
    {
        // Check if accommodation exists first
        var existingAccommodation = await _accommodationRepository.GetByIdAsync(accommodationDto.Id);
        if (existingAccommodation == null)
        {
            throw new EntityNotFoundException("Accommodation", accommodationDto.Id);
        }

        // Validate all references using the validation service
        await _validationService.ValidateAccommodationReferencesAsync(
            accommodationDto.RegionId,
            accommodationDto.AccommodationTypeId,
            accommodationDto.DensityId,
            accommodationDto.DurationIds,
            accommodationDto.AmenityIds);

        var accommodation = _mapper.Map<Accommodation>(accommodationDto);

        // Attach existing duration entities so EF Core updates the join table correctly
        var durations = await _durationRepository.GetAllAsync(d => accommodationDto.DurationIds.Contains(d.Id));
        accommodation.Duration = durations.ToList();

        // Attach amenity options based on provided IDs
        var amenityOptions = accommodationDto.AmenityIds.Any()
            ? await _amenityOptionsRepository.GetAllAsync(ao => accommodationDto.AmenityIds.Contains(ao.AmenityId))
            : Enumerable.Empty<AmenityOptions>();
        accommodation.AmenityOptions = amenityOptions.ToList();
        accommodation.CreatedAt = existingAccommodation.CreatedAt;

        // Set timestamps for updated entity using validation service
        // _validationService.SetTimestampsForUpdate(accommodation, existingAccommodation);

        await _accommodationRepository.UpdateAsync(accommodation);
        return _mapper.Map<AccommodationDto>(accommodation);
    }

    public async Task<bool> Delete(long id)
    {
        // Check if accommodation exists first
        var exists = await _accommodationRepository.GetByIdAsync(id);
        if (exists == null)
        {
            return false;
        }

        await _accommodationRepository.DeleteAsync(id);
        return true;
    }

    public async Task<(List<AccommodationDto> results, int totalCount)> SearchAccommodationsAsync(AccommodationSearchRequestDto request)
    {
        // Start with base predicate for soft delete filter
        Expression<Func<Accommodation, bool>> predicate = a => a.DiscardedAt == null;

        // Add search term filter if provided (search in name and address fields)
        if (!string.IsNullOrWhiteSpace(request.SearchTerm))
        {
            predicate = predicate.And(a =>
                a.Name.Contains(request.SearchTerm) ||
                a.StreetLine1.Contains(request.SearchTerm) ||
                (a.StreetLine2 != null && a.StreetLine2.Contains(request.SearchTerm)) ||
                a.Suburb.Contains(request.SearchTerm) || a.Postcode.Contains(request.SearchTerm) ||
                a.Region.Name.Contains(request.SearchTerm));
        }

        if (request.AccommodationTypeIds is { Count: > 0 })
        {
            // Add accommodation type filter if provided
            predicate = predicate.And(a => request.AccommodationTypeIds.Contains(a.AccommodationTypeId));
        }

        // Add amenity filter if provided
        if (request.AmenityIds is { Count: > 0 })
        {
            predicate = predicate.And(a =>
                request.AmenityIds.Any(amenityId =>
                    a.AmenityOptions.Any(ao => ao.AmenityId == amenityId)));
        }

        // Fetch accommodations from repository - use spatial query if distance filtering is requested
        IEnumerable<Accommodation> accommodations;
        if (request.Latitude.HasValue && request.Longitude.HasValue && request.Radius.HasValue)
        {
            // Use spatial query filtering for distance queries
            accommodations = await _accommodationRepository.GetWithinDistanceAsync(
                predicate,
                request.Latitude.Value,
                request.Longitude.Value,
                request.Radius.Value,
                a => a.Region);
        }
        else
        {
            // Use standard query for non-spatial searches
            accommodations = await _accommodationRepository.GetAllAsync(predicate, a => a.Region);
        }

        var accommodationsList = accommodations.ToList();

        // Get total count before pagination
        var totalCount = accommodationsList.Count;

        // Apply sorting
        var sortedAccommodations = ApplySorting(accommodationsList.AsQueryable(),
            request.Sorting?.OrderBy ?? "name",
            request.Sorting?.Direction ?? "asc");

        // Apply pagination
        var results = sortedAccommodations
            .Skip((request.Pagination.Page - 1) * request.Pagination.PageSize)
            .Take(request.Pagination.PageSize)
            .ToList();

        return (_mapper.Map<List<AccommodationDto>>(results), totalCount);
    }

    private static IQueryable<Accommodation> ApplySorting(IQueryable<Accommodation> query, string sortBy, string sortDirection)
    {
        var isDescending = sortDirection.Equals("desc", StringComparison.OrdinalIgnoreCase);

        return sortBy.ToLower() switch
        {
            "name" => isDescending ? query.OrderByDescending(a => a.Name) : query.OrderBy(a => a.Name),
            "suburb" => isDescending ? query.OrderByDescending(a => a.Suburb) : query.OrderBy(a => a.Suburb),
            "state" => isDescending ? query.OrderByDescending(a => a.State) : query.OrderBy(a => a.State),
            "postcode" => isDescending ? query.OrderByDescending(a => a.Postcode) : query.OrderBy(a => a.Postcode),
            _ => query.OrderBy(a => a.Name) // Default sort by name
        };
    }
}
