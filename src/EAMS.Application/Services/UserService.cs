﻿using AutoMapper;
using EAMS.Application.DTOs;
using EAMS.Domain.Aggregates;
using EAMS.Domain.Entities;
using EAMS.Domain.Exceptions;
using EAMS.Domain.Interfaces;
using EAMS.Domain.Repositories;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using GraphUser = Microsoft.Graph.Models.User;

namespace EAMS.Application.Services;

public class UserService : IUserService
{
    private readonly IGraphService _graphService;
    private readonly IUserRepository _userRepository;
    private readonly IUserInvitationRepository _userInvitationRepository;
    private readonly IOrganisationRepository _organisationRepository;
    private readonly IOrganisationDomainService _organisationDomainService;
    private readonly ILogger<UserService> _logger;
    private readonly IConfiguration _configuration;
    private readonly IMapper _mapper;

    public UserService(IGraphService graphClient,
        IUserRepository userRepository,
        IUserInvitationRepository userInvitationRepository,
        IOrganisationRepository organisationRepository,
        IOrganisationDomainService organisationDomainService,
        IConfiguration configuration,
        IMapper mapper,
        ILogger<UserService> logger)
    {
        _graphService = graphClient;
        _userRepository = userRepository;
        _userInvitationRepository = userInvitationRepository;
        _organisationRepository = organisationRepository;
        _logger = logger;
        _configuration = configuration;
        _mapper = mapper;
        _organisationDomainService = organisationDomainService;
    }

    public async Task<UserDto?> GetCurrentLoginUserAsync()
    {
        var graphUser = await _graphService.GetCurrentLoginUserAsync();

        if (graphUser is not null && !string.IsNullOrEmpty(graphUser.Id))
        {
            var userId = Guid.Parse(graphUser.Id);
            var user = await _userRepository.GetByIdAsync(userId, usr => usr.Organisation);

            if (user is not null)
            {
                graphUser.CompanyName = user.Organisation?.Name;
                user.GraphUser = graphUser;
                return _mapper.Map<UserDto>(user);
            }
            else
            {
                // check if the any invitation for this user using query invitedUserId == userId, include target organisation navigation object
                var invitationResults = await _userInvitationRepository
                    .GetAllAsync(ui => ui.InvitedUserId == userId, ui => ui.TargetOrganisation);
                var existingInvitation = invitationResults.FirstOrDefault();

                // create a new user in the database.
                var newUser = new User
                {
                    Id = userId,
                    OrganisationId = existingInvitation?.TargetOrganisationId ?? null,
                };

                await _userRepository.AddAsync(newUser);
                newUser.GraphUser = graphUser;
                return _mapper.Map<UserDto>(newUser);
            }
        }

        return null;
    }

    public async Task<UserDto?> GetUserByIdAsync(Guid userId)
    {
        var graphUser = await _graphService.GetUserByIdAsync(userId);
        if (graphUser is not null)
        {
            var userRecord = await this.LoadGraphUserRecordAsync(graphUser);
            return _mapper.Map<UserDto>(userRecord);
        }

        return null;
    }


    public async Task<InvitationDto?> CreateInvitationAsync(InvitationDto dto)
    {
        var userInvitation = _mapper.Map<UserInvitation>(dto);

        // check if the targetOrganisationId exists
        var organisation = await _organisationRepository.GetByIdAsync(userInvitation.TargetOrganisationId.Value);
        if (organisation == null)
            throw new InvalidDataException("TargetOrganisationId does not exists");

        // Convert newInvitation into Invitation and call GraphService.CreateInvitation
        var invitation =
            await _graphService.InviteUserAsync(userInvitation.InvitedUserEmailAddress, userInvitation.InviteRedirectUrl);

        // Convert invitation to UserInvitation class update Invitation to database
        if (invitation is not null && !string.IsNullOrEmpty(invitation.Id))
        {
            userInvitation.Id = Guid.Parse(invitation.Id);
            userInvitation.InvitedUserId = !string.IsNullOrEmpty(invitation.InvitedUser?.Id)
                ? Guid.Parse(invitation.InvitedUser.Id)
                : Guid.Empty;
            var currentUser = await this.GetCurrentLoginUserAsync();
            userInvitation.InvitedByUserId = currentUser?.Id ?? Guid.Empty;

            // check if invitation exists
            var existingInvitation = await _userInvitationRepository.GetByIdAsync(userInvitation.Id);
            if (existingInvitation is null)
            {
                // Add new user to groups.
                var invitedToGroups = userInvitation.Roles.Split(',');
                foreach (var groupName in invitedToGroups)
                {
                    var group = await _graphService.GetGroupByNameAsync(groupName);
                    if (group is not null)
                    {
                        await _graphService.AddUserToGroupAsync(Guid.Parse(invitation.InvitedUser.Id), Guid.Parse(group.Id));
                    }
                }

                // record invitation
                await _userInvitationRepository.AddAsync(userInvitation);
            }
            else
            {
                // just send another invite and update the record
                await _userInvitationRepository.UpdateAsync(userInvitation);
            }
        }

        return dto;
    }

    public async Task<UserDto?> UpdateUserDetailsAsync(UserDto dto)
    {
        // check if user exists
        var targetUser = await _userRepository.GetByIdAsync(dto.Id);
        if (targetUser is null)
            throw new EntityNotFoundException("User", dto.Id);
  
        Organisation foundOrganisation = null;

        if (!string.IsNullOrEmpty(dto.CompanyName))
        {
            foundOrganisation = await _organisationRepository.GetOrganisationByNameAsync(dto.CompanyName);
            if (foundOrganisation is null)
                throw new InvalidDataException("Organisation does not exists");
        }

        var graphUser = _mapper.Map<GraphUser>(dto);
        var updatedGraphUser = await _graphService.PatchUserDetailsAsync(graphUser);
        // Once the user details is updated in Azure AD, update the user record in the database if the organisationId is changed.
        targetUser.OrganisationId = foundOrganisation?.Id ?? null;
        await _userRepository.UpdateAsync(targetUser);
        targetUser.GraphUser = updatedGraphUser;

        return _mapper.Map<UserDto>(targetUser);
    }

    public async Task AddUserToGroupAsync(Guid userId, string groupName)
    {
        // create a task list and execute them in parallel
        var targetUserTask = _graphService.GetUserByIdAsync(userId);
        var groupTask = _graphService.GetGroupByNameAsync(groupName);
        var tasks = new List<Task>() { targetUserTask, groupTask };
        await Task.WhenAll(tasks);

        if (targetUserTask.Result is null)
            throw new EntityNotFoundException("GraphUser", userId);

        if (groupTask.Result is null)
            throw new EntityNotFoundException("GraphGroup", groupName);

        await _graphService.AddUserToGroupAsync(Guid.Parse(targetUserTask.Result.Id), Guid.Parse(groupTask.Result.Id));
    }

    public async Task RemoveUserFromGroupAsync(Guid userId, string groupName)
    {
        // create a task list and execute them in parallel
        var targetUserTask = _graphService.GetUserByIdAsync(userId);
        var groupTask = _graphService.GetGroupByNameAsync(groupName);
        var tasks = new List<Task>() { targetUserTask, groupTask };
        await Task.WhenAll(tasks);

        if (targetUserTask.Result is null)
            throw new EntityNotFoundException("GraphUser", userId);

        if (groupTask.Result is null)
            throw new EntityNotFoundException("GraphGroup", groupName);

        await _graphService.RemoveUserFromGroupAsync(Guid.Parse(targetUserTask.Result.Id), Guid.Parse(groupTask.Result.Id));
    }

    public async Task<List<UserDto>> GetAllUsersAsync()
    {
        var usersGroupName = _configuration.GetSection("GraphApi:RolesMapping:Users").Value;
        var graphUsers = await _graphService.GetUsersInGroupAsync(usersGroupName);

        if (graphUsers is not null && graphUsers.Any())
        {
            var sortedUsers = graphUsers.OrderBy(gu => gu.DisplayName);
            var dbUsers = await _userRepository.GetAllAsync(null, usr => usr.Organisation);
            var userList = sortedUsers.Select(gu =>
            {
                var dbUser = dbUsers.FirstOrDefault(u => u.Id == Guid.Parse(gu.Id));
                if (dbUser is not null)
                {
                    dbUser.GraphUser = gu;
                    return dbUser;
                }
                else
                {
                    return new User
                    {
                        Id = Guid.Parse(gu.Id),
                        GraphUser = gu
                    };
                }
            }).ToList();

            return _mapper.Map<List<UserDto>>(userList);
        }

        return null;
    }

    public async Task<UserDto?> GetUserByEmailAsync(string email)
    {
        var graphUser = await _graphService.GetUserByEmailAsync(email);
        if (graphUser is not null)
        {
            var user = await this.LoadGraphUserRecordAsync(graphUser);
            return _mapper.Map<UserDto>(user);
        }

        return null;
    }

    /// <summary>
    /// Get User Record by GraphUser obj.
    /// </summary>
    /// <param name="graphUser"></param>
    /// <returns></returns>
    private async Task<User> LoadGraphUserRecordAsync(GraphUser graphUser)
    {
        // get User record from database
        var userId = Guid.Parse(graphUser.Id);
        var user = await _userRepository.GetByIdAsync(userId, usr => usr.Organisation);

        if (user is not null)
        {
            graphUser.CompanyName = user.Organisation?.Name;
            user.GraphUser = graphUser;
            return user;
        }

        return null;
    }

    public async Task DeleteUserAsync(Guid userId)
    {
        await _graphService.DeleteUserAsync(userId);
        await _userRepository.DeleteAsync(userId);
    }

    public async Task<(List<UserDto> results, int totalCount)> SearchUsersAsync(
        string? searchTerm, string? searchProperty,
        int? pageNumber, int? pageSize, string sortBy, string sortDirection)
    {
        string userGroup = _configuration.GetSection("GraphApi:RolesMapping:Users").Value;

        var (graphUsers, totalCount) = await _graphService.SearchAndSortUsersInGroupAsync(userGroup,
            searchTerm, searchProperty, sortBy, sortDirection, pageNumber ?? 1, pageSize ?? 50);


        if (graphUsers is not null && graphUsers.Any())
        {
            // var users = graphUsers.Select(gu => new User
            // {
            //     Id = Guid.Parse(gu.Id),
            //     GraphUser = gu
            // });

            var userIds = graphUsers.Select(gu => Guid.Parse(gu.Id)).ToList();
            var users = await _userRepository.GetAllAsync(u => userIds.Contains(u.Id), u => u.Organisation);
            foreach (var user in users)
            {
                var matchingGraphUser = graphUsers.FirstOrDefault(gu => gu.Id == user.Id.ToString());
                if (matchingGraphUser != null)
                {
                    matchingGraphUser.CompanyName = user.Organisation?.Name;
                    user.GraphUser = matchingGraphUser;
                }
            }

            return (_mapper.Map<List<UserDto>>(users.ToList()), totalCount);
        }

        return (_mapper.Map<List<UserDto>>(new List<User>()), totalCount);
    }

    public async Task<bool> CheckIfCurrentUserCanManageTargetUser(Guid targetUserId)
    {
        // check if user exists
        var user = await _userRepository.GetByIdAsync(targetUserId);
        if (user is null)
            throw new EntityNotFoundException("User", targetUserId);

        if (user.OrganisationId.HasValue && await _organisationDomainService.CanUserManageOrganisationAsync(user.OrganisationId.Value) == false)
        {
            return false;
        }

        return true;
    }
}