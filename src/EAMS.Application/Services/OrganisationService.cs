using EAMS.Application.Interfaces;
using EAMS.Domain.Exceptions;
using Microsoft.Extensions.Logging;
using System.Linq.Expressions;
using LinqKit;
using EAMS.Domain.Repositories;
using EAMS.Domain.Aggregates;
using EAMS.Application.DTOs;
using AutoMapper;
using EAMS.Domain.Services;
using EAMS.Domain.Interfaces;
using System;

namespace EAMS.Application.Services;

public class OrganisationService : IOrganisationService
{
    private readonly IOrganisationRepository _organisationRepository;
    private readonly ILogger<OrganisationService> _logger;
    private readonly IUserRepository _userRepository;
    private readonly IMapper _mapper;
    private readonly IGraphService _graphService;
    private readonly IOrganisationDomainService _organisationDomainService;

    public OrganisationService(IOrganisationRepository organisationRepository,
        IUserRepository userRepository,
        IMapper mapper,
        ILogger<OrganisationService> logger,
        IGraphService graphService,
        IOrganisationDomainService organisationDomainService)
    {
        _organisationRepository = organisationRepository;
        _logger = logger;
        _userRepository = userRepository;
        _mapper = mapper;
        _graphService = graphService;
        _organisationDomainService = organisationDomainService;
    }

    public async Task<IEnumerable<OrganisationDetailsDto>> GetAll()
    {   
        var organisations = await _organisationRepository.GetAllAsync(includes: org => org.ParentOrg);
        return _mapper.Map<IEnumerable<OrganisationDetailsDto>>(organisations.ToList());
    }

    public async Task<OrganisationDetailsDto?> GetById(Guid id)
    {
        var org = await _organisationRepository.GetByIdAsync(id, includes: org => org.ParentOrg);
        if (org == null)
            return null;
        return _mapper.Map<OrganisationDetailsDto?>(org);
    }

    public async Task<OrganisationDetailsDto> Create(CreateOrganisationDto dto)
    {
        var organisation = _mapper.Map<Organisation>(dto);
        organisation.Id = Guid.NewGuid();
        // AddAsync returns void and handles SaveChanges internally
        await _organisationRepository.AddAsync(organisation);
        
        // Return the organisation with its generated ID
        return _mapper.Map<OrganisationDetailsDto>(organisation);
    }

    public async Task<OrganisationDetailsDto> Update(UpdateOrganisationDto dto)
    {
        // Check if organisation exists first
        var existingOrganisation = await _organisationRepository.GetByIdAsync(dto.Id);
        if (existingOrganisation == null)
        {
            throw new EntityNotFoundException("Organisation", dto.Id);
        }


        // UpdateAsync returns void and handles SaveChanges internally
        var organisation = _mapper.Map<Organisation>(dto);
        await _organisationRepository.UpdateAsync(organisation);

        return _mapper.Map<OrganisationDetailsDto>(organisation);
    }

    public async Task<bool> Delete(Guid id)
    {
        // Check if organisation exists first
        var exists = await _organisationRepository.GetByIdAsync(id);
        if (exists == null)
        {
            throw new EntityNotFoundException("Organisation", id);
        }

        // DeleteAsync returns void and handles SaveChanges internally
        await _organisationRepository.DeleteAsync(id);

        return true;
    }

    public async Task<List<UserDto>> GetOrganisationUsers(Guid orgId)
    {
        // get the organisations and its children
        var organisation = await _organisationRepository.GetByIdAsync(orgId, org => org.ChildOrganisations);
        if (organisation is null)
            throw new InvalidDataException($"Could not find organisation with id {orgId}");

        List<Guid> orgIds = new List<Guid>() { organisation.Id };
        if (organisation.ChildOrganisations.Any())
        {
            orgIds.AddRange(organisation.ChildOrganisations.Select(co => co.Id));
        }

        var users = await GetUsersByOrganisationIdsAsync(orgIds);

        return users;
    }

    private async Task<List<UserDto>> GetUsersByOrganisationIdsAsync(List<Guid> orgIds)
    {
        // find users with orgIds
        var users = await _userRepository.GetAllAsync(u => u.OrganisationId != null && orgIds.Contains(u.OrganisationId.Value), u => u.Organisation);
        var graphUsers = await _graphService.GetUsersByIdsAsync(users.Select(u => u.Id).ToList());

        graphUsers.ForEach(gu =>
        {
            var user = users.FirstOrDefault(u => u.Id.Equals(Guid.Parse(gu.Id)));
            if (user is not null)
                user.GraphUser = gu;
        });

        var userList = users.Where(u => { return u.GraphUser is not null; }).ToList();
        return _mapper.Map<List<UserDto>>(userList);
    }   

    public async Task<IEnumerable<OrganisationDetailsDto>> GetSubOrganisations(Guid parentOrgId)
    {
        var childOrgs = await _organisationRepository.GetAllAsync(org => org.ParentOrgId == parentOrgId);
        return _mapper.Map<IEnumerable<OrganisationDetailsDto>>(childOrgs.ToList());
    }

    public async Task<(List<OrganisationDetailsDto> results, int totalCount)> SearchOrganisationAsync(string? searchTerm, int pageNumber, int pageSize, string sortBy, string sortDirection)
    {
        Expression<Func<Organisation, bool>> predicate = o => o.DiscardedAt == null;

        if (!string.IsNullOrWhiteSpace(searchTerm))
        {
           predicate = predicate.And(o =>
                o.Name.Contains(searchTerm) ||
                o.StreetLine1.Contains(searchTerm) ||
                (o.StreetLine2 != null && o.StreetLine2.Contains(searchTerm)) ||
                o.State.Contains(searchTerm) ||
                o.Postcode.Contains(searchTerm) ||
                o.Suburb.Contains(searchTerm) ||
                (o.Service != null && o.Service.Contains(searchTerm)) ||
                (o.ClientGroup != null && o.ClientGroup.Contains(searchTerm)) ||
                (o.KeyContactName != null && o.KeyContactName.Contains(searchTerm)) ||
                (o.KeyContactPhone != null && o.KeyContactPhone.Contains(searchTerm)) ||
                (o.KeyContactEmail != null && o.KeyContactEmail.Contains(searchTerm)));
        }

        // Fetch queryable from repository
        var query = await _organisationRepository.GetAllAsync(predicate, o => o.ParentOrg);

        // Get total count before pagination
        var totalCount = query.Count();

        // Apply sorting
        query = ApplySorting(query.AsQueryable(), sortBy, sortDirection);

        // Apply pagination
        var results = query
            .Skip((pageNumber - 1) * pageSize)
            .Take(pageSize)
            .ToList();

        return (_mapper.Map<List<OrganisationDetailsDto>>(results), totalCount);
    }

    private IQueryable<Organisation> ApplySorting(IQueryable<Organisation> query, string sortBy, string sortDirection)
    {
        var isDescending = sortDirection.Equals("desc", StringComparison.OrdinalIgnoreCase);

        return sortBy.ToLower() switch
        {
            "name" => isDescending ? query.OrderByDescending(o => o.Name) : query.OrderBy(o => o.Name),
            "state" => isDescending ? query.OrderByDescending(o => o.State) : query.OrderBy(o => o.State),
            "suburb" => isDescending ? query.OrderByDescending(o => o.Suburb) : query.OrderBy(o => o.Suburb),
            "postcode" => isDescending ? query.OrderByDescending(o => o.Postcode) : query.OrderBy(o => o.Postcode),
            _ => query.OrderBy(o => o.Name) // Default sort
        };
    }

    public async Task<bool> CheckIfCurrentUserCanManageOrganisation(Guid organisationId)
    {
        return await _organisationDomainService.CanUserManageOrganisationAsync(organisationId);
    }
}
