using System.ComponentModel;
using System.Globalization;

namespace EAMS.Application.Converters;

/// <summary>
/// TypeConverter that converts comma-delimited strings to List&lt;int&gt; and vice versa
/// </summary>
public class CommaDelimitedListConverter : TypeConverter
{
    public override bool CanConvertFrom(ITypeDescriptorContext? context, Type sourceType)
    {
        return sourceType == typeof(string) || base.CanConvertFrom(context, sourceType);
    }

    public override bool CanConvertTo(ITypeDescriptorContext? context, Type? destinationType)
    {
        return destinationType == typeof(string) || base.CanConvertTo(context, destinationType);
    }

    public override object? ConvertFrom(ITypeDescriptorContext? context, CultureInfo? culture, object value)
    {
        if (value is string stringValue)
        {
            if (string.IsNullOrWhiteSpace(stringValue))
            {
                return new List<int>();
            }

            try
            {
                return stringValue
                    .Split(',', StringSplitOptions.RemoveEmptyEntries)
                    .Select(s => s.Trim())
                    .Where(s => !string.IsNullOrEmpty(s))
                    .Select(int.Parse)
                    .ToList();
            }
            catch (FormatException)
            {
                throw new FormatException($"Unable to parse '{stringValue}' as comma-delimited list of integers.");
            }
        }

        return base.ConvertFrom(context, culture, value);
    }

    public override object? ConvertTo(ITypeDescriptorContext? context, CultureInfo? culture, object? value, Type destinationType)
    {
        if (destinationType == typeof(string) && value is List<int> list)
        {
            return string.Join(",", list);
        }

        return base.ConvertTo(context, culture, value, destinationType);
    }
}
