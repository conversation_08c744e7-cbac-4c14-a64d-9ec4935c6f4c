using Microsoft.Graph.Models;

namespace EAMS.Domain.Interfaces;

public interface IGraphService
{
    public Task<User?> GetCurrentLoginUserAsync();
    public Task<User?> GetUserByIdAsync(Guid userId);
    public Task<User?> GetUserByEmailAsync(string email);
    public Task<Invitation?> InviteUserAsync(string email, string redirectUrl);
    public Task<Group?> GetGroupByNameAsync(string groupName);
    public Task AddUserToGroupAsync(Guid userId, Guid groupId);
    public Task RemoveUserFromGroupAsync(Guid userId, Guid groupId);
    public Task<User?> PatchUserDetailsAsync(User graphUser);
    public Task DeleteUserAsync(Guid userId);

    public Task<(List<User> Results, int TotalCount)> SearchAndSortUsersInGroupAsync(
            string groupName,
            string searchTerm,
            string searchProperty,     // displayName, mail, jobTitle, mobilePhone, phoneNumber
            string orderByProperty,    // same options as above
            string orderDirection,     // "asc" or "desc"
            int pageNumber,
            int pageSize
    );

    public Task<List<User>> GetUsersByIdsAsync(List<Guid> userIds);
    
    public Task<List<User>> GetUsersInGroupAsync(string groupName);
}