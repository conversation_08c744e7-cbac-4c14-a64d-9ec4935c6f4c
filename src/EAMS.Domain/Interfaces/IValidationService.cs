using EAMS.Domain.Entities;
using EAMS.Domain.Repositories;

namespace EAMS.Domain.Interfaces;

/// <summary>
/// Service for handling common validation operations across the application
/// </summary>
public interface IValidationService
{
    /// <summary>
    /// Validates that an entity with the specified ID exists in the repository
    /// </summary>
    /// <typeparam name="TEntity">The entity type</typeparam>
    /// <typeparam name="TKey">The key type</typeparam>
    /// <param name="repository">The repository to check</param>
    /// <param name="id">The entity ID to validate</param>
    /// <param name="entityName">The name of the entity for error messages</param>
    /// <returns>Task that completes when validation is done</returns>
    /// <exception cref="EAMS.Domain.Exceptions.EntityNotFoundException">Thrown when entity is not found</exception>
    Task ValidateEntityExistsAsync<TEntity, TKey>(
        IRepository<TEntity, TKey> repository, 
        TKey id, 
        string entityName)
        where TEntity : BaseEntity<TKey>
        where T<PERSON><PERSON> : struct;

    /// <summary>
    /// Validates that all entities with the specified IDs exist in the repository
    /// </summary>
    /// <typeparam name="TEntity">The entity type</typeparam>
    /// <typeparam name="TKey">The key type</typeparam>
    /// <param name="repository">The repository to check</param>
    /// <param name="ids">The entity IDs to validate</param>
    /// <param name="entityName">The name of the entity for error messages</param>
    /// <returns>Task that completes when validation is done</returns>
    /// <exception cref="EAMS.Domain.Exceptions.EntityNotFoundException">Thrown when any entity is not found</exception>
    Task ValidateEntitiesExistAsync<TEntity, TKey>(
        IRepository<TEntity, TKey> repository, 
        IEnumerable<TKey> ids, 
        string entityName)
        where TEntity : BaseEntity<TKey>
        where TKey : struct;

    /// <summary>
    /// Sets timestamps for a new entity (CreatedAt and UpdatedAt)
    /// </summary>
    /// <typeparam name="TKey">The key type</typeparam>
    /// <param name="entity">The entity to set timestamps for</param>
    void SetTimestampsForCreate<TKey>(BaseEntity<TKey> entity) where TKey : struct;

    /// <summary>
    /// Sets timestamps for an updated entity (UpdatedAt only, preserves CreatedAt)
    /// </summary>
    /// <typeparam name="TKey">The key type</typeparam>
    /// <param name="entity">The entity to set timestamps for</param>
    /// <param name="existingEntity">The existing entity to preserve CreatedAt from</param>
    void SetTimestampsForUpdate<TKey>(BaseEntity<TKey> entity, BaseEntity<TKey> existingEntity) where TKey : struct;

    /// <summary>
    /// Validates accommodation-specific references (Region, AccommodationType, Density, Durations)
    /// </summary>
    /// <param name="regionId">Region ID to validate</param>
    /// <param name="accommodationTypeId">Accommodation type ID to validate</param>
    /// <param name="densityId">Density ID to validate</param>
    /// <param name="durationIds">Duration IDs to validate</param>
    /// <param name="amenityOptionIds">Amenity option IDs to validate</param>
    /// <returns>Task that completes when validation is done</returns>
    Task ValidateAccommodationReferencesAsync(
        int regionId,
        int accommodationTypeId,
        int densityId,
        IEnumerable<int> durationIds,
        IEnumerable<long> amenityOptionIds);
}
