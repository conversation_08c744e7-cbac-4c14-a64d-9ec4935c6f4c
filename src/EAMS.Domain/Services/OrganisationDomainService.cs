﻿using EAMS.Domain.Aggregates;
using EAMS.Domain.Interfaces;
using EAMS.Domain.Repositories;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace EAMS.Domain.Services
{
    public class OrganisationDomainService : IOrganisationDomainService
    {
        private readonly IOrganisationRepository _organisationRepository;
        private readonly IUserRepository _userRepository;
        private readonly IGraphService _graphService;

        public OrganisationDomainService(
            IGraphService graphService,
            IOrganisationRepository organisationRepository,
            IUserRepository userRepository
            )
        {
            _graphService = graphService;
            _organisationRepository = organisationRepository;
            _userRepository = userRepository;
        }


        public async Task<bool> CanUserManageOrganisationAsync(Guid orgId)
        {
            var getOrgTask = _organisationRepository.GetAllAsync(org => org.Id == orgId || org.ParentOrgId == orgId);
            var getUserTask = this.GetCurrentLoginRecord();

            await Task.WhenAll(new List<Task>() { getOrgTask, getUserTask });

            var orgIds = getOrgTask.Result.Select(org => org.Id);
            var currentUser = getUserTask.Result;

            if (currentUser is null)
                return false;
 
            return currentUser.OrganisationId.HasValue && orgIds.Contains(currentUser.OrganisationId.Value);
        }

        private async Task<User> GetCurrentLoginRecord()
        {
            var getUserTask = _graphService.GetCurrentLoginUserAsync();

            var graphUser = getUserTask.Result;
            if (graphUser == null)
                return null;

            return await _userRepository.GetByIdAsync(Guid.Parse(graphUser.Id));
        }
    }
}
