using EAMS.Domain.Interfaces;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Microsoft.Graph;
using Microsoft.Graph.Models;

namespace EAMS.Domain.Services;

public class GraphService : IGraphService
{
    private readonly GraphServiceClient _graphClient;
    private readonly GraphServiceClient _applicationClient;
    private readonly IMemoryCache _cache;
    private readonly ILogger<GraphService> _logger;
    private readonly int _cacheDurationInMinutes;

    private readonly string[] _userSelectProperties = new string[] { "displayName", "givenName", "surname", "userPrincipalName", "id", "jobTitle", "companyName", "mobilePhone", "businessPhone", "mail" };
    private readonly string[] _userSearchableProperties = new string[] { "displayName", "givenName", "surname", "mail", "mobilePhone", "businessPhone" };

    public GraphService(
        GraphServiceClient graphClient,
        IMemoryCache cache,
        ILogger<GraphService> logger,
        IConfiguration configuration,
        [FromKeyedServices("applicationGraph")] GraphServiceClient appGraphClient)
    {
        _graphClient = graphClient;
        _logger = logger;
        _cache = cache;
        _cacheDurationInMinutes = int.Parse(configuration.GetSection("CacheDuractionInMinutes").Value);
        _applicationClient = appGraphClient;
    }

    public async Task<User?> GetCurrentLoginUserAsync()
    {
        var graphUser = await _graphClient.Me.GetAsync(requestConfiguration =>
        {
            requestConfiguration.QueryParameters.Select = this._userSelectProperties;
        });
        return graphUser;
    }

    public async Task<User?> GetUserByEmailAsync(string email)
    {
        var searchResult = await _graphClient.Users.GetAsync(requestConfiguration =>
        {
            requestConfiguration.QueryParameters.Filter = $"mail eq '{email}'";
            requestConfiguration.QueryParameters.Select = this._userSelectProperties;
        });

        if (searchResult.Value != null && searchResult.Value.Count > 0)
        {
            return searchResult.Value.First();
        }

        return null;
    }

    public Task<Invitation?> InviteUserAsync(string email, string redirectUrl)
    {
        var invitation = new Invitation
        {
            InvitedUserEmailAddress = email,
            InviteRedirectUrl = redirectUrl,
            SendInvitationMessage = true,
            InvitedUserMessageInfo = new InvitedUserMessageInfo
            {
                // TODO: Store message body in the database settings table.
                CustomizedMessageBody = "You are invited to use EAMS."
            }
        };

        return _graphClient.Invitations.PostAsync(invitation);
    }

    public async Task<Group?> GetGroupByNameAsync(string groupName)
    {
        try
        {
            var groups = await _graphClient.Groups.GetAsync(requestConfiguration =>
            {
                requestConfiguration.QueryParameters.Filter = $"displayName eq '{groupName}'";
            });

            // Return the first group found 
            return groups.Value.FirstOrDefault();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"Error when getting group by name");
            throw;
        }
    }

    public async Task AddUserToGroupAsync(Guid userId, Guid groupId)
    {
        var requestBody = new ReferenceCreate
        {
            OdataId = $"https://graph.microsoft.com/v1.0/directoryObjects/{userId}"
        };

        await _applicationClient.Groups[groupId.ToString()].Members.Ref.PostAsync(requestBody);
    }

    public Task<User?> GetUserByIdAsync(Guid userId)
    {
        return _graphClient.Users[userId.ToString()].GetAsync(requestConfiguration =>
        {
            requestConfiguration.QueryParameters.Select = this._userSelectProperties;
        });
    }

    public Task<User?> PatchUserDetailsAsync(User graphUser)
    {
        return _graphClient.Users[graphUser.Id].PatchAsync(graphUser);
    }

    public async Task<(List<User> Results, int TotalCount)> SearchAndSortUsersInGroupAsync(
        string groupName,
        string searchTerm,
        string searchProperty,
        string orderByProperty,
        string orderDirection,
        int pageNumber,
        int pageSize)
    {
        if (!string.IsNullOrEmpty(searchProperty) && _userSearchableProperties.Any(prop => prop.Equals(searchProperty, StringComparison.OrdinalIgnoreCase)) == false)
            throw new InvalidDataException($"Search property '{searchProperty}' is not allowed.");

        // Step 1: Get all Users
        var allUsers = await this.GetUsersInGroupAsync(groupName);
        
        // Step 2: Filter users by search property
        var filteredUsers = string.IsNullOrEmpty(searchTerm) ? allUsers : allUsers.Where(user =>
        {
            return searchProperty.ToLower() switch
            {
                "displayname" => user.DisplayName?.Contains(searchTerm, StringComparison.OrdinalIgnoreCase) == true,
                "surname" => user.Surname?.Contains(searchTerm, StringComparison.OrdinalIgnoreCase) == true,
                "givenName" => user.GivenName?.Contains(searchTerm, StringComparison.OrdinalIgnoreCase) == true,
                "mail" => user.Mail?.Contains(searchTerm, StringComparison.OrdinalIgnoreCase) == true,
                "jobtitle" => user.JobTitle?.Contains(searchTerm, StringComparison.OrdinalIgnoreCase) == true,
                "mobilephone" => user.MobilePhone?.Contains(searchTerm, StringComparison.OrdinalIgnoreCase) == true,
                "phonenumber" => user.BusinessPhones?.Any(p => p.Contains(searchTerm, StringComparison.OrdinalIgnoreCase)) == true,
                ""  => user.DisplayName?.Contains(searchTerm, StringComparison.OrdinalIgnoreCase) == true ||
                       user.Mail?.Contains(searchTerm, StringComparison.OrdinalIgnoreCase) == true || 
                       user.JobTitle?.Contains(searchTerm, StringComparison.OrdinalIgnoreCase) == true,
                _ => false
            };
        });

        // Step 3: Sort users
        if (string.IsNullOrEmpty(orderByProperty))
        {
            orderByProperty = "displayName";
            orderDirection = "asc";
        }

        var sortedUsers = orderDirection.ToLower() == "desc"
            ? filteredUsers.OrderByDescending(user => GetPropertyValue(user, orderByProperty)).ToList()
            : filteredUsers.OrderBy(user => GetPropertyValue(user, orderByProperty)).ToList();

        // Step 5: Apply pagination
        var pagedUsers = sortedUsers
            .Skip((pageNumber - 1) * pageSize)
            .Take(pageSize)
            .ToList();

        return (pagedUsers, sortedUsers.Count);

    }

    // Helper to get property value dynamically
    private string GetPropertyValue(User user, string property)
    {
        return property.ToLower() switch
        {
            "displayname" => user.DisplayName,
            "givenname" => user.GivenName,
            "surname" => user.Surname,
            "mail" => user.Mail,
            "jobtitle" => user.JobTitle,
            "mobilephone" => user.MobilePhone,
            "phonenumber" => user.BusinessPhones?.FirstOrDefault(),
            _ => null
        };
    }

    public async Task<List<User>> GetUsersByIdsAsync(List<Guid> userIds)
    {
        var filterString = $"id in ({string.Join(",", userIds.Select(id => $"'{id}'"))})";

        var users = await _applicationClient.Users
            .GetAsync(config =>
            {
                config.QueryParameters.Filter = filterString;
            });
        return users.Value as List<User>;
    }

    public async Task<List<User>> GetUsersInGroupAsync(string groupName)
    {
        string cacheKeys = $"group-users: {groupName}";
        var allUsers = new List<User>();
        // Step 1: Try to get cached users
        if (this._cache.TryGetValue(cacheKeys, out allUsers) == false)
        {
            allUsers = new List<User>();
            var group = await GetGroupByNameAsync(groupName);
            if (group == null)
                throw new InvalidDataException($"Could not find group with name: {groupName}");

            // Step 2: Retrieve all members using OdataNextLink
            var page = await _applicationClient.Groups[group.Id].Members.GetAsync(config =>
            {
                config.QueryParameters.Top = 50;
            });

            while (page != null)
            {
                var users = page.Value.OfType<User>();
                allUsers.AddRange(users);

                if (page.OdataNextLink != null)
                {
                    page = await _applicationClient.Groups[group.Id].Members
                        .WithUrl(page.OdataNextLink)
                        .GetAsync();
                }
                else
                {
                    break;
                }
            }

            this._cache.Set(cacheKeys, allUsers, TimeSpan.FromMinutes(_cacheDurationInMinutes)); 
        }

        return allUsers;
    }

    public async Task RemoveUserFromGroupAsync(Guid userId, Guid groupId)
    {
        await _applicationClient.Groups[groupId.ToString()]
            .Members[userId.ToString()]
            .Ref
            .DeleteAsync();
    }

    public async Task DeleteUserAsync(Guid userId)
    {
        // remove user from group memberships first
        var groups = await _applicationClient.Users[userId.ToString()]
            .MemberOf
            .GetAsync();

        foreach (var group in groups.Value.OfType<Group>())
        {
            try
            {
                await this.RemoveUserFromGroupAsync(userId, Guid.Parse(group.Id));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error removing user ({userId}) from group ({group.DisplayName}).");
            }
        }

        await _applicationClient.Users[userId.ToString()]
            .DeleteAsync();
    }
}