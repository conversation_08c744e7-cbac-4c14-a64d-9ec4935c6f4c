using EAMS.Domain.Interfaces;
using EAMS.Domain.Entities;
using EAMS.Domain.Exceptions;
using EAMS.Domain.Repositories;
using EAMS.Domain.ValueObjects;
using System.Linq;

namespace EAMS.Domain.Services;

/// <summary>
/// Service for handling common validation operations across the application
/// </summary>
public class ValidationService : IValidationService
{
    private readonly IRepository<Region, int> _regionRepository;
    private readonly IRepository<AccommodationType, int> _accommodationTypeRepository;
    private readonly IRepository<Density, int> _densityRepository;
    private readonly IRepository<Duration, int> _durationRepository;
    private readonly IAmenityOptionsRepository _amenityOptionsRepository;

    public ValidationService(
        IRepository<Region, int> regionRepository,
        IRepository<AccommodationType, int> accommodationTypeRepository,
        IRepository<Density, int> densityRepository,
        IRepository<Duration, int> durationRepository,
        IAmenityOptionsRepository amenityOptionsRepository)
    {
        _regionRepository = regionRepository;
        _accommodationTypeRepository = accommodationTypeRepository;
        _densityRepository = densityRepository;
        _durationRepository = durationRepository;
        _amenityOptionsRepository = amenityOptionsRepository;
    }

    /// <inheritdoc />
    public async Task ValidateEntityExistsAsync<TEntity, TKey>(
        IRepository<TEntity, TKey> repository, 
        TKey id, 
        string entityName)
        where TEntity : BaseEntity<TKey>
        where TKey : struct
    {
        var entity = await repository.GetByIdAsync(id);
        if (entity == null)
        {
            throw new EntityNotFoundException(entityName, id);
        }
    }

    /// <inheritdoc />
    public async Task ValidateEntitiesExistAsync<TEntity, TKey>(
        IRepository<TEntity, TKey> repository, 
        IEnumerable<TKey> ids, 
        string entityName)
        where TEntity : BaseEntity<TKey>
        where TKey : struct
    {
        foreach (var id in ids)
        {
            await ValidateEntityExistsAsync(repository, id, entityName);
        }
    }

    /// <inheritdoc />
    public void SetTimestampsForCreate<TKey>(BaseEntity<TKey> entity) where TKey : struct
    {
        var now = DateTime.UtcNow;
        entity.CreatedAt = now;
        entity.UpdatedAt = now;
    }

    /// <inheritdoc />
    public void SetTimestampsForUpdate<TKey>(BaseEntity<TKey> entity, BaseEntity<TKey> existingEntity) where TKey : struct
    {
        entity.UpdatedAt = DateTime.UtcNow;
        entity.CreatedAt = existingEntity.CreatedAt; // Preserve original creation timestamp
    }

    /// <inheritdoc />
    public async Task ValidateAccommodationReferencesAsync(
        int regionId,
        int accommodationTypeId,
        int densityId,
        IEnumerable<int> durationIds,
        IEnumerable<long> amenityOptionIds)
    {
        // Execute validations sequentially because EF Core DbContexts used by the repositories are not thread-safe.
        await ValidateEntityExistsAsync(_regionRepository, regionId, "Region");
        await ValidateEntityExistsAsync(_accommodationTypeRepository, accommodationTypeId, "AccommodationType");
        await ValidateEntityExistsAsync(_densityRepository, densityId, "Density");
        await ValidateEntitiesExistAsync(_durationRepository, durationIds, "Duration");

        if (amenityOptionIds.Any())
        {
            await ValidateEntitiesExistAsync(_amenityOptionsRepository, amenityOptionIds, "AmenityOption");
        }
    }
}
