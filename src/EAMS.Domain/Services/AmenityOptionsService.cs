using EAMS.Domain.Interfaces;
using EAMS.Domain.Exceptions;
using EAMS.Domain.Repositories;
using EAMS.Domain.ValueObjects;

namespace EAMS.Domain.Services;

public class AmenityOptionsService : IAmenityOptionsService
{
    private readonly IAmenityOptionsRepository _amenityOptionsRepository;
    private readonly IAmenityRepository _amenityRepository;

    public AmenityOptionsService(IAmenityOptionsRepository amenityOptionsRepository, IAmenityRepository amenityRepository)
    {
        _amenityOptionsRepository = amenityOptionsRepository;
        _amenityRepository = amenityRepository;
    }

    public async Task<IEnumerable<AmenityOptions>> GetAll()
    {
        return await _amenityOptionsRepository.GetAllAsync();
    }

    public async Task<AmenityOptions?> GetById(Int64 id)
    {
        return await _amenityOptionsRepository.GetByIdAsync(id);
    }

    public async Task<AmenityOptions> Create(AmenityOptions amenityOption)
    {
        // Validate that the referenced amenity exists
        var amenityExists = await _amenityRepository.GetByIdAsync(amenityOption.AmenityId);
        if (amenityExists == null)
        {
            throw new EntityNotFoundException("Amenity", amenityOption.AmenityId);
        }

        // Set timestamps for new entity
        amenityOption.CreatedAt = DateTime.UtcNow;
        amenityOption.UpdatedAt = DateTime.UtcNow;

        // AddAsync returns void and handles SaveChanges internally
        await _amenityOptionsRepository.AddAsync(amenityOption);

        // Return the amenity option with its generated ID
        return amenityOption;
    }

    public async Task<AmenityOptions> Update(AmenityOptions amenityOption)
    {
        // Check if amenity option exists first
        var existingAmenityOption = await _amenityOptionsRepository.GetByIdAsync(amenityOption.Id);
        if (existingAmenityOption == null)
        {
            throw new EntityNotFoundException("AmenityOption", amenityOption.Id);
        }

        // Validate that the referenced amenity exists
        var amenityExists = await _amenityRepository.GetByIdAsync(amenityOption.AmenityId);
        if (amenityExists == null)
        {
            throw new EntityNotFoundException("Amenity", amenityOption.AmenityId);
        }

        // Update timestamp
        amenityOption.UpdatedAt = DateTime.UtcNow;
        // Preserve original creation timestamp
        amenityOption.CreatedAt = existingAmenityOption.CreatedAt;

        // UpdateAsync returns void and handles SaveChanges internally
        await _amenityOptionsRepository.UpdateAsync(amenityOption);

        return amenityOption;
    }

    public async Task<bool> Delete(Int64 id)
    {
        // Check if amenity option exists first
        var exists = await _amenityOptionsRepository.GetByIdAsync(id);
        if (exists == null)
        {
            return false;
        }

        // DeleteAsync returns void and handles SaveChanges internally
        await _amenityOptionsRepository.DeleteAsync(id);
        return true;
    }
}
