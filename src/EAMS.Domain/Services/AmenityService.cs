using EAMS.Domain.Interfaces;
using EAMS.Domain.Exceptions;
using EAMS.Domain.Repositories;
using EAMS.Domain.ValueObjects;

namespace EAMS.Domain.Services;

public class AmenityService : IAmenityService
{
    private readonly IAmenityRepository _amenityRepository;

    public AmenityService(IAmenityRepository amenityRepository)
    {
        _amenityRepository = amenityRepository;
    }

    public async Task<IEnumerable<Amenity>> GetAll()
    {
        return await _amenityRepository.GetAllAsync();
    }

    public async Task<Amenity?> GetById(Int64 id)
    {
        return await _amenityRepository.GetByIdAsync(id);
    }

    public async Task<Amenity> Create(Amenity amenity)
    {
        // Set timestamps for new entity
        amenity.CreatedAt = DateTime.UtcNow;
        amenity.UpdatedAt = DateTime.UtcNow;

        // AddAsync returns void and handles SaveChanges internally
        await _amenityRepository.AddAsync(amenity);

        // Return the amenity with its generated ID
        return amenity;
    }

    public async Task<Amenity> Update(Amenity amenity)
    {
        // Check if amenity exists first
        var existingAmenity = await _amenityRepository.GetByIdAsync(amenity.Id);
        if (existingAmenity == null)
        {
            throw new EntityNotFoundException("Amenity", amenity.Id);
        }

        // Update timestamp
        amenity.UpdatedAt = DateTime.UtcNow;
        // Preserve original creation timestamp
        amenity.CreatedAt = existingAmenity.CreatedAt;

        // UpdateAsync returns void and handles SaveChanges internally
        await _amenityRepository.UpdateAsync(amenity);

        return amenity;
    }

    public async Task<bool> Delete(Int64 id)
    {
        // Check if amenity exists first
        var exists = await _amenityRepository.GetByIdAsync(id);
        if (exists == null)
        {
            return false;
        }

        // DeleteAsync returns void and handles SaveChanges internally
        await _amenityRepository.DeleteAsync(id);
        return true;
    }
}
