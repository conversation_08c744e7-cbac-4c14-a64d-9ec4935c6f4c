using EAMS.Domain.Aggregates;
using EAMS.Domain.Entities;

namespace EAMS.Domain.ValueObjects;

public abstract class EnumEntity : SoftDeletableEntity<int>
{
    public string Name { get; set; } = string.Empty;
}

public class AccommodationType : EnumEntity
{
    public ICollection<Accommodation> Accommodations { get; set; } = new List<Accommodation>();
}
public class Density : EnumEntity
{
    public ICollection<Accommodation> Accommodations { get; set; } = new List<Accommodation>();
}

public class Region : EnumEntity
{
    public ICollection<Accommodation> Accommodations { get; set; } = new List<Accommodation>();
}

public class Duration : EnumEntity
{
    public ICollection<Accommodation> Accommodations { get; set; } = new List<Accommodation>();
}

public class AmenityType : EnumEntity
{
    public ICollection<Amenity> Amenities { get; set; } = new List<Amenity>();
}
