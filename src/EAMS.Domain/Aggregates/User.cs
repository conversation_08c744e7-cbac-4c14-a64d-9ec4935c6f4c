using System.ComponentModel.DataAnnotations.Schema;
using EAMS.Domain.Entities;
using GraphUser = Microsoft.Graph.Models.User;
namespace EAMS.Domain.Aggregates;

public class User : SoftDeletableEntity<Guid>
{
    public Guid? OrganisationId { get; set; }

    [NotMapped]
    public GraphUser GraphUser { get; set; }

    // Navigation properties for relationships
    public Organisation Organisation { get; set; }
    public ICollection<UserInvitation> InvitationsSent { get; set; } = new List<UserInvitation>();
}