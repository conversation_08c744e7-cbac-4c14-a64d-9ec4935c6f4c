﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <Compile Remove="Constants\**" />
    <EmbeddedResource Remove="Constants\**" />
    <None Remove="Constants\**" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="LinqKit.Microsoft.EntityFrameworkCore" Version="9.0.8" />
    <PackageReference Include="Microsoft.Extensions.Caching.Memory" Version="9.0.9" />
    <PackageReference Include="Microsoft.Extensions.Configuration.Abstractions" Version="9.0.8" />
    <PackageReference Include="Microsoft.Graph" Version="5.90.0" />
  </ItemGroup>

</Project>
