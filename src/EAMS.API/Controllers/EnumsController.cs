using EAMS.Application.DTOs;
using EAMS.Application.Interfaces;
using EAMS.Domain.Exceptions;
using EAMS.Domain.ValueObjects;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System.Diagnostics.Eventing.Reader;

namespace EAMS.API.Controllers
{
    [Route("api/[controller]")]
    public class EnumsController : Controller
    {
        private readonly ILogger<EnumsController> _logger;
        private readonly IValueObjectsService _valueObjectService;

        public EnumsController(ILogger<EnumsController> logger,
            IValueObjectsService valueObjectService
        )
        {
            _logger = logger;
            _valueObjectService = valueObjectService;
        }

        /// <summary>
        /// Get all accommodation types enums
        /// </summary>
        /// <returns></returns>
        [HttpGet("accommodation-types")]
        public async Task<ActionResult<IEnumerable<EnumDto>>> GetAccommodationTypes()
        {
            var accommodationTypes = await _valueObjectService.GetAll<AccommodationType>();
            return Ok(accommodationTypes.ToList());
        }

        /// <summary>
        /// Get all amenity types enums
        /// </summary>
        /// <returns></returns>
        [HttpGet("amenity-types")]
        public async Task<ActionResult<IEnumerable<EnumDto>>> GetAmenityTypes()
        {
            var amenityTypes = await _valueObjectService.GetAll<AmenityType>();
            return Ok(amenityTypes.ToList());
        }

        /// <summary>
        /// Get all density enums
        /// </summary>
        /// <returns></returns>
        [HttpGet("densities")]
        public async Task<ActionResult<IEnumerable<EnumDto>>> GetDensities()
        {
            var densities = await _valueObjectService.GetAll<Density>();
            return Ok(densities.ToList());
        }

        /// <summary>
        /// Get all duration enums
        /// </summary>
        /// <returns></returns>
        [HttpGet("durations")]
        public async Task<ActionResult<IEnumerable<EnumDto>>> GetDurations()
        {
            var durations = await _valueObjectService.GetAll<Duration>();
            return Ok(durations.ToList());
        }

        /// <summary>
        /// Get all region enums
        /// </summary>
        /// <returns></returns>
        [HttpGet("regions")]
        public async Task<ActionResult<IEnumerable<EnumDto>>> GetRegions()
        {
            var regions = await _valueObjectService.GetAll<Region>();
            return Ok(regions.ToList());
        }

        /// <summary>
        /// Create a new accommodation type enum
        /// </summary>
        /// <param name="dto"></param>
        /// <returns></returns>
        [Authorize(Roles = "Administrators")]
        [HttpPost("accommodation-types")]
        public async Task<ActionResult<EnumDto>> CreateAccommodationType([FromBody] EnumDto dto)
        {
            var accommodationType = new AccommodationType
            {
                Name = dto.Name
            };
            var newEnum = await _valueObjectService.Create<AccommodationType>(accommodationType);
            return Ok(newEnum);
        }

        /// <summary>
        /// Create a new amenity type enum
        /// </summary>
        /// <param name="dto"></param>
        /// <returns></returns>
        [Authorize(Roles = "Administrators")]
        [HttpPost("amenity-types")]
        public async Task<ActionResult<EnumDto>> CreateAmenityType([FromBody] EnumDto dto)
        {
            var amenityType = new AmenityType
            {
                Name = dto.Name
            };
            var newEnum = await _valueObjectService.Create<AmenityType>(amenityType);
            return Ok(newEnum);
        }

        /// <summary>
        /// Create a new density enum
        /// </summary>
        /// <param name="dto"></param>
        /// <returns></returns>
        [Authorize(Roles = "Administrators")]
        [HttpPost("densities")]
        public async Task<ActionResult<EnumDto>> CreateDensity([FromBody] EnumDto dto)
        {
            var density = new Density
            {
                Name = dto.Name
            };
            var newEnum = await _valueObjectService.Create<Density>(density);
            return Ok(newEnum);
        }

        /// <summary>
        /// Create a new duration enum
        /// </summary>
        /// <param name="dto"></param>
        /// <returns></returns>
        [Authorize(Roles = "Administrators")]
        [HttpPost("durations")]
        public async Task<ActionResult<EnumDto>> CreateDuration([FromBody] EnumDto dto)
        {
            var duration = new Duration
            {
                Name = dto.Name
            };
            var newEnum = await _valueObjectService.Create<Duration>(duration);
            return Ok(newEnum);
        }

        /// <summary>
        /// Create a new region enum
        /// </summary>
        /// <param name="dto"></param>
        /// <returns></returns>
        [Authorize(Roles = "Administrators")]
        [HttpPost("regions")]
        public async Task<ActionResult<EnumDto>> CreateRegion([FromBody] EnumDto dto)
        {
            var region = new Region
            {
                Name = dto.Name
            };
            var newEnum = await _valueObjectService.Create<Region>(region);
            return Ok(newEnum);
        }

        /// <summary>
        /// Update an accommodation type enum
        /// </summary>
        /// <param name="id"></param>
        /// <param name="dto"></param>
        /// <returns></returns>
        [Authorize(Roles = "Administrators")]
        [HttpPut("accommodation-types/{id}")]
        public async Task<ActionResult<EnumDto>> UpdateAccommodationType(int id, [FromBody] EnumDto dto)
        {
            try
            {
                var accommodationType = new AccommodationType
                {
                    Id = id,
                    Name = dto.Name
                };
                var updatedEnum = await _valueObjectService.Update(accommodationType);
                return Ok(updatedEnum);
            }
            catch (EntityNotFoundException ex)
            {
                return NotFound(ex.Message);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating accommodation type with ID {Id}", id);
                return StatusCode(500, "An error occurred while updating the accommodation type.");
            }
        }

        /// <summary>
        /// Update an amenity type enum
        /// </summary>
        /// <param name="id"></param>
        /// <param name="dto"></param>
        /// <returns></returns>
        [Authorize(Roles = "Administrators")]
        [HttpPut("amenity-types/{id}")]
        public async Task<ActionResult<EnumDto>> UpdateAmenityType(int id, [FromBody] EnumDto dto)
        {
            try
            {
                var amenityType = new AmenityType
                {
                    Id = id,
                    Name = dto.Name
                };
                var updatedEnum = await _valueObjectService.Update(amenityType);
                return Ok(updatedEnum);
            }
            catch (EntityNotFoundException ex)
            {
                return NotFound(ex.Message);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating amenity type with ID {Id}", id);
                return StatusCode(500, "An error occurred while updating the amenity type.");
            }
        }

        /// <summary>
        /// Update a density enum
        /// </summary>
        /// <param name="id"></param>
        /// <param name="dto"></param>
        /// <returns></returns>
        [Authorize(Roles = "Administrators")]
        [HttpPut("densities/{id}")]
        public async Task<ActionResult<EnumDto>> UpdateDensity(int id, [FromBody] EnumDto dto)
        {
            try
            {
                var density = new Density
                {
                    Id = id,
                    Name = dto.Name
                };
                var updatedEnum = await _valueObjectService.Update(density);
                return Ok(updatedEnum);
            }
            catch (EntityNotFoundException ex)
            {
                return NotFound(ex.Message);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating density with ID {Id}", id);
                return StatusCode(500, "An error occurred while updating the density.");
            }

        }

        /// <summary>
        /// Update a duration enum
        /// </summary>
        /// <param name="id"></param>
        /// <param name="dto"></param>
        /// <returns></returns>
        [Authorize(Roles = "Administrators")]
        [HttpPut("durations/{id}")]
        public async Task<ActionResult<EnumDto>> UpdateDuration(int id, [FromBody] EnumDto dto)
        {
            try
            {
                var duration = new Duration
                {
                    Id = id,
                    Name = dto.Name
                };
                var updatedEnum = await _valueObjectService.Update(duration);
                return Ok(updatedEnum);
            }
            catch (EntityNotFoundException ex) { return NotFound(ex.Message); }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating duration with ID {Id}", id);
                return StatusCode(500, "An error occurred while updating the duration.");
            }
        }

        /// <summary>
        /// Update a region enum
        /// </summary>
        /// <param name="id"></param>
        /// <param name="dto"></param>
        /// <returns></returns>
        [Authorize(Roles = "Administrators")]
        [HttpPut("regions/{id}")]
        public async Task<ActionResult<EnumDto>> UpdateRegion(int id, [FromBody] EnumDto dto)
        {
            try
            {
                var region = new Region
                {
                    Id = id,
                    Name = dto.Name
                };
                var updatedEnum = await _valueObjectService.Update(region);

                return Ok(updatedEnum);
            }
            catch (EntityNotFoundException ex) { return NotFound(ex.Message); }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating region with ID {Id}", id);
                return StatusCode(500, "An error occurred while updating the region.");
            }
        }

        /// <summary>
        /// Delete an accommodation type enum
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [Authorize(Roles = "Administrators")]
        [HttpDelete("accommodation-types/{id}")]
        public async Task<IActionResult> DeleteAccommodationType(int id)
        {
            try
            {
                await _valueObjectService.Delete<AccommodationType>(id);
                return Ok();
            }
            catch (EntityNotFoundException ex)
            {
                return NotFound(ex.Message);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting accommodation type with ID {Id}", id);
                return StatusCode(500, "An error occurred while deleting the accommodation type.");
            }
        }

        /// <summary>
        /// Delete an amenity type enum
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [Authorize(Roles = "Administrators")]
        [HttpDelete("amenity-types/{id}")]
        public async Task<IActionResult> DeleteAmenityType(int id)
        {
            try
            {
                await _valueObjectService.Delete<AmenityType>(id);
                return Ok();
            }
            catch (EntityNotFoundException ex)
            {
                return NotFound(ex.Message);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting amenity type with ID {Id}", id);
                return StatusCode(500, "An error occurred while deleting the amenity type.");
            }
        }

        /// <summary>
        /// Delete a density enum
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [Authorize(Roles = "Administrators")]
        [HttpDelete("densities/{id}")]
        public async Task<IActionResult> DeleteDensity(int id)
        {
            try
            {
                await _valueObjectService.Delete<Density>(id);
                return Ok();
            }
            catch (EntityNotFoundException ex)
            { return NotFound(ex.Message); }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting density with ID {Id}", id);
                return StatusCode(500, "An error occurred while deleting the density.");
            }
        }

        /// <summary>
        /// Delete a duration enum
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [Authorize(Roles = "Administrators")]
        [HttpDelete("durations/{id}")]
        public async Task<IActionResult> DeleteDuration(int id)
        {
            try
            {
                await _valueObjectService.Delete<Duration>(id);
                return Ok();
            }
            catch (EntityNotFoundException ex) { return NotFound(ex.Message); }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting duration with ID {Id}", id);
                return StatusCode(500, "An error occurred while deleting the duration.");
            }
        }

        /// <summary>
        /// Delete a region enum
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [Authorize(Roles = "Administrators")]
        [HttpDelete("regions/{id}")]
        public async Task<IActionResult> DeleteRegion(int id)
        {
            try
            {
                _valueObjectService.Delete<Region>(id);
                return Ok();
            }
            catch (EntityNotFoundException ex) { return NotFound(ex.Message); }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting region with ID {Id}", id);
                return StatusCode(500, "An error occurred while deleting the region.");
            }
        }

    }
}