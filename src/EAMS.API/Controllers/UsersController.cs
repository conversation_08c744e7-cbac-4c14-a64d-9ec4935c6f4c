﻿using EAMS.Application.DTOs;
using EAMS.Domain.Interfaces;
using EAMS.Domain.Aggregates;
using EAMS.Domain.Entities;
using EAMS.Domain.Exceptions;
using EAMS.Application.Interfaces;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Graph;
using GraphUser = Microsoft.Graph.Models.User;

namespace EAMS.API.Controllers;

/// <summary>
/// Manages Users' related operations.
/// </summary>
[Authorize]
[Route("api/[controller]")]
[ApiController]
public class UsersController : ControllerBase
{
    private readonly IUserService _userService;
    private readonly IOrganisationService _orgService;

    public UsersController(IUserService userService, IOrganisationService organisationService)
    {
        _userService = userService;
        _orgService = organisationService;
    }

    /// <summary>
    /// Retrieves current logged-in user's profile.
    /// </summary>
    /// <returns>A User object if found, otherwise 404</returns>
    [HttpGet("me")]
    public async Task<ActionResult<UserDto>> GetUserProfile()
    {
        try
        {
            var me = await _userService.GetCurrentLoginUserAsync();
            if (me == null)
            {
                return NotFound("User profile not found");
            }

            return Ok(me);
        }
        catch (ServiceException ex)
        {
            return StatusCode(ex.ResponseStatusCode != 0 ? (int)ex.ResponseStatusCode : 500, ex.Message);
        }
        catch (Exception ex)
        {
            return StatusCode(500, $"An unexpected error occurred: {ex.Message}");
        }
    }

    /// <summary>
    /// Get user by its id
    /// </summary>
    /// <param name="id">UserId as Guid</param>
    /// <returns></returns>
    [HttpGet("{id}")]
    public async Task<ActionResult<UserDto>> GetUserById(Guid id)
    {
        try
        {
            var foundUser = await _userService.GetUserByIdAsync(id);
            if (foundUser is null)
                return NotFound($"Could not find user with id: {id}");

            return Ok(foundUser);
        }
        catch (Exception ex)
        {
            return StatusCode(500, $"An unexpected error occurred: {ex.Message}");
        }
    }

    /// <summary>
    /// Update user's details.
    /// </summary>
    /// <param name="id">UserId (Guid)</param>
    /// <param name="userDto">Updated details</param>
    /// <returns></returns>
    [Authorize(Roles = "Administrators, Managers")]
    [HttpPatch("{id}")]
    public async Task<ActionResult<UserDto>> UpdateUserDetailsById(Guid id, [FromBody] UserDto userDto)
    {
        try
        {
            if (userDto.Id != id)
                return BadRequest("User ID in the URL does not match the ID in the body.");

            if (!HttpContext.User.IsInRole("Administrators"))
            {
                if (await _userService.CheckIfCurrentUserCanManageTargetUser(id) == false)
                {
                    return Unauthorized();
                }
            }

            var updatedUser = await _userService.UpdateUserDetailsAsync(userDto);

            return Ok(updatedUser);
        }
        catch (EntityNotFoundException ex)
        {
            return NotFound(ex.Message);
        }
        catch (ServiceException ex)
        {
            return StatusCode(ex.ResponseStatusCode != 0 ? (int)ex.ResponseStatusCode : 500, ex.Message);
        }
        catch (Exception ex)
        {
            return StatusCode(500, $"An unexpected error occurred: {ex.Message}");
        }
    }

    /// <summary>
    /// Delete a user
    /// </summary>
    /// <param name="id">Target UserId (Guid)</param>
    /// <returns></returns>
    [Authorize(Roles = "Administrators, Managers")]
    [HttpDelete("{id}")]
    public async Task<IActionResult> DeleteUser(Guid id)
    {
        try
        {
            if (!HttpContext.User.IsInRole("Administrators"))
            {
                if (await _userService.CheckIfCurrentUserCanManageTargetUser(id) == false)
                {
                    return Unauthorized();
                }
            }

            await _userService.DeleteUserAsync(id);

            // Only allow updating certain fields
            return Ok();
        }
        catch (EntityNotFoundException ex)
        {
            return NotFound(ex.Message);
        }
        catch (ServiceException ex)
        {
            return StatusCode(ex.ResponseStatusCode != 0 ? (int)ex.ResponseStatusCode : 500, ex.Message);
        }
        catch (Exception ex)
        {
            return StatusCode(500, $"An unexpected error occurred: {ex.Message}");
        }
    }

    /// <summary>
    /// Create an invitation for a new user to join the system. If the email address has been invited before, it will resend the invitation.
    /// </summary>
    /// <remarks>
    /// This operation is restricted to users with the "Managers" role.
    /// </remarks>
    /// <param name="dto">An Invitation Object</param>
    /// <returns>Upon successful, it will create return invitation object with invitation id</returns>
    [Authorize(Roles = "Managers")]
    [HttpPost("invite")]
    public async Task<ActionResult<InvitationDto>> InviteUser(InvitationDto dto)
    {
        try
        {
            var userInvitation = await _userService.CreateInvitationAsync(dto);
            return Ok(userInvitation);
        }
        catch (InvalidDataException dataEx)
        {
            return BadRequest(dataEx.Message);
        }
        catch (Exception ex)
        {
            return StatusCode(500, $"An unexpected error occurred: {ex.Message}");
        }
    }

    /// <summary>
    /// Get all users
    /// </summary>
    /// <returns></returns>
    [Authorize(Roles = "Administrators")]
    [HttpGet]
    public async Task<ActionResult<IEnumerable<UserDto>>> GetAllUsers()
    {
        try
        {
            var users = await _userService.GetAllUsersAsync();
            if (users is null || !users.Any())
            {
                return NotFound("Users not found");
            }

            return Ok(users);
        }
        catch (Exception ex)
        {
            return StatusCode(500, $"An unexpected error occurred: {ex.Message}");
        }
    }

    /// <summary>
    /// Adds an existing user to a specified group.
    /// </summary>
    /// <remarks>
    /// This operation is restricted to users with the "Managers" role.
    /// </remarks>
    /// <param name="requestDto"></param>
    /// <returns></returns>
    [Authorize(Roles = "Administrators, Managers")]
    [HttpPost("addUserToGroup")]
    public async Task<IActionResult> AddUserToGroup([FromBody] AddUserToGroupDto requestDto)
    {
        try
        {
            if (!HttpContext.User.IsInRole("Administrators"))
            {
                if (await _userService.CheckIfCurrentUserCanManageTargetUser(requestDto.UserId) == false)
                {
                    return Unauthorized();
                }
            }

            await _userService.AddUserToGroupAsync(requestDto.UserId, requestDto.GroupName);
            return Ok();

        }
        catch (EntityNotFoundException ex)
        {
            return NotFound(ex.Message);
        }
        catch (Exception ex)
        {
            return StatusCode(500, $"An unexpected error occurred: {ex.Message}");
        }
    }

    /// <summary>
    /// Remove an existing user from Group
    /// </summary>
    /// <remarks>
    /// This operation is restricted to users with the "Managers" role.
    /// </remarks>
    /// <param name="requestDto"></param>
    /// <returns></returns>
    [Authorize(Roles = "Administrators, Managers")]
    [HttpPost("removeUserFromGroup")]
    public async Task<IActionResult> RemoveUserFromgroup([FromBody] RemoveUserFromGroupDto requestDto)
    {
        if (!HttpContext.User.IsInRole("Administrators"))
        {
            if (await _userService.CheckIfCurrentUserCanManageTargetUser(requestDto.UserId) == false)
            {
                return Unauthorized();
            }
        }

        await _userService.RemoveUserFromGroupAsync(requestDto.UserId, requestDto.GroupName);
        return Ok();
    }

    /// <summary>
    /// Update current logged-in user's profile details.
    /// </summary>
    /// <param name="userDto"></param>
    /// <returns></returns>
    [HttpPatch("me")]
    public async Task<ActionResult<UserDto>> UpdateUserProfile([FromBody] UserDto userDto)
    {
        try
        {
            var currentUser = await _userService.GetCurrentLoginUserAsync();
            if (currentUser == null)
            {
                return NotFound("Current user not found");
            }

            if (userDto.Id != currentUser.Id)
                return Unauthorized();

            // Only allow updating certain fields
            var updatedUser = await _userService.UpdateUserDetailsAsync(userDto);

            return Ok(updatedUser);
        }
        catch (ServiceException ex)
        {
            return StatusCode(ex.ResponseStatusCode != 0 ? (int)ex.ResponseStatusCode : 500, ex.Message);
        }
        catch (Exception ex)
        {
            return StatusCode(500, $"An unexpected error occurred: {ex.Message}");
        }
    }


    /// <summary>
    /// Get all active users registers in the system.
    /// Pagination queries are optional, by default it will be sorted by Users' display name.
    /// </summary>
    /// <remarks>
    /// This method is restricted to Administrators only
    /// </remarks>
    /// <param name="requestDto"></param>
    /// <returns></returns>
    [Authorize(Roles = "Administrators")]
    [HttpGet("search")]
    public async Task<ActionResult<SearchResultDto<UserDto>>> SearchUsers(
        [FromQuery] UserSearchRequestDto requestDto)
    {
        try
        {
            if (!requestDto.ValidateQuery())
                return BadRequest(
                    "Invalid query. Pagination.Page and Pagination.PageSize are required fields and must be greater than 0");

            var searchTerm = string.Empty;
            var searchProperty = string.Empty;

            if (!string.IsNullOrEmpty(requestDto.DisplayName))
            {
                searchTerm = requestDto.DisplayName;
                searchProperty = nameof(requestDto.DisplayName);
            }
            else if (!string.IsNullOrEmpty(requestDto.Email))
            {
                searchTerm = requestDto.Email;
                searchProperty = nameof(requestDto.Email);
            }
            else
                searchTerm = requestDto.SearchTerm;

            var (users, totalCount) = await _userService.SearchUsersAsync(
                searchTerm, searchProperty,
                requestDto.Pagination.Page, requestDto.Pagination.PageSize, requestDto.Sorting.OrderBy, requestDto.Sorting.Direction);


            var result = new SearchResultDto<UserDto>(
                users, requestDto.Pagination.Page, requestDto.Pagination.PageSize, totalCount,
                requestDto.Sorting.OrderBy, requestDto.Sorting.Direction);

            return Ok(result);
        }
        catch (Exception ex)
        {
            return StatusCode(500, $"An unexpected error occurred: {ex.Message}");
        }
    }
}