using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using EAMS.Application.DTOs;
using EAMS.Domain.Interfaces;
using EAMS.Domain.Exceptions;
using AutoMapper;
using EAMS.Domain.ValueObjects;

namespace EAMS.API.Controllers;

[Authorize]
[ApiController]
[Route("api/[controller]")]
public class AmenityOptionsController : ControllerBase
{
    private readonly IAmenityOptionsService _amenityOptionsService;
    private readonly ILogger<AmenityOptionsController> _logger;
    private readonly IMapper _mapper;

    public AmenityOptionsController(
        IAmenityOptionsService amenityOptionsService,
        ILogger<AmenityOptionsController> logger,
        IMapper mapper)
    {
        _amenityOptionsService = amenityOptionsService;
        _logger = logger;
        _mapper = mapper;
    }

    /// <summary>
    /// Get all amenity options
    /// </summary>
    [HttpGet]
    [Authorize(Roles = "Users, Managers")]
    public async Task<ActionResult<IEnumerable<AmenityOptionsDto>>> GetAmenityOptions()
    {
        var amenityOptions = await _amenityOptionsService.GetAll();
        var response = _mapper.Map<IEnumerable<AmenityOptionsDto>>(amenityOptions);
        return Ok(response);
    }

    /// <summary>
    /// Get amenity option by ID
    /// </summary>
    [HttpGet("{id}")]
    [Authorize(Roles = "Users, Managers")]
    public async Task<ActionResult<AmenityOptionsDto>> GetAmenityOption(Int64 id)
    {
        var amenityOption = await _amenityOptionsService.GetById(id);

        if (amenityOption == null)
        {
            throw new EntityNotFoundException("AmenityOption", id);
        }

        var response = _mapper.Map<AmenityOptionsDto>(amenityOption);
        return Ok(response);
    }

    /// <summary>
    /// Create a new amenity option
    /// </summary>
    [HttpPost]
    [Authorize(Roles = "Managers")]
    public async Task<ActionResult<AmenityOptionsDto>> CreateAmenityOption(AmenityOptionsDto amenityOptionsDto)
    {
        var amenityOption = _mapper.Map<AmenityOptions>(amenityOptionsDto);
        var createdAmenityOption = await _amenityOptionsService.Create(amenityOption);
        var response = _mapper.Map<AmenityOptionsDto>(createdAmenityOption);

        return CreatedAtAction(nameof(GetAmenityOption), new { id = createdAmenityOption.Id }, response);
    }

    /// <summary>
    /// Update an existing amenity option
    /// </summary>
    [HttpPut("{id}")]
    [Authorize(Roles = "Managers")]
    public async Task<ActionResult<AmenityOptionsDto>> UpdateAmenityOption(Int64 id, AmenityOptionsDto amenityOptionsDto)
    {
        var amenityOption = _mapper.Map<AmenityOptions>(amenityOptionsDto);
        var updatedAmenityOption = await _amenityOptionsService.Update(amenityOption);
        var response = _mapper.Map<AmenityOptionsDto>(updatedAmenityOption);

        return Ok(response);
    }

    /// <summary>
    /// Delete an amenity option
    /// </summary>
    [HttpDelete("{id}")]
    [Authorize(Roles = "Managers")]
    public async Task<ActionResult<bool>> DeleteAmenityOption(Int64 id)
    {
        var result = await _amenityOptionsService.Delete(id);
        return Ok(result);
    }
}
